name: Docker Build & Deploy
on:  
  push:
    branches: ['main']
permissions:
  contents: read
  packages: write
jobs:
  build:
    runs-on: uhg-runner-m
    outputs:
      branch: ${{ steps.date.outputs.branch }}
      docker_image: docker.repo1.uhc.com/magnus-oec/magnus_oec_api:${{ steps.date.outputs.image_tag}}
    steps:
    - uses: actions/checkout@v3
      with:
        ref: ${{ github.event.inputs.branch }}
    - name: Get current date
      id: date
      run: |
        echo "branch=main" >> $GITHUB_OUTPUT
        echo "image_tag=main_$(date +'%Y-%m-%d-%H-%M-%S')" >> $GITHUB_OUTPUT
    - name: Login to Repo1 Container Registry
      uses: docker/login-action@v2
      with:
          registry: optum-docker-auth-prod.repo1.uhc.com
          username: ${{ secrets.SVC_BUILD_USERID }}
          password: ${{ secrets.SVC_BUILD_PWD }}
    - name: Docker Build And Push
      uses: optum-eeps/epl-actions/docker-build-push@v1
      with:
          docker-image: magnus-oec/magnus_oec_api
          docker-dockerfile: "./Dockerfile"
          docker-tag: ${{ steps.date.outputs.image_tag}}
          registry-user: ${{ secrets.SVC_BUILD_USERID }}
          registry-token: ${{ secrets.SVC_BUILD_PWD }}
          twistcli-user: "ms\\${{ secrets.SVC_BUILD_USERID }}"
          twistcli-password: ${{ secrets.SVC_BUILD_PWD }}
          enable-twistcli: true
          enable-xray: true
          docker-push: true
          severity: critical
  Deploy_to_Dev:
      runs-on: uhg-runner
      needs: build
      steps:
      - run: echo ${{needs.build.outputs.docker_image}}
      - uses: actions/checkout@v3
        with:
          ref: ${{needs.build.outputs.branch}}
      - uses: azure/setup-kubectl@v3
        with:
          version: latest
      - name: Set Kube Config
        shell: bash
        run: |
          mkdir ~/.kube
          pwd 
          if [ -n "${{ inputs.kube-config-file }}" ]; then
            cp ${{ inputs.kube-config-file }} ~/.kube/config
          elif [ -n "${{ inputs.kube-config }}" ]; then
            echo "${{ inputs.kube-config }}" > ~/.kube/config
          else
  
            touch ~/.kube/config
            export KUBECONFIG=~/.kube/config 
            echo "${{ secrets.K8S_CERTS }}" | base64 --decode > cert.crt
            kubectl config set-credentials magnus-oec-dev --token="${{ secrets.K8S_TOKEN }}"
            kubectl config set-cluster ctcnonprdusr101 --server=https://10.29.113.253:443 --certificate-authority=cert.crt
  
            kubectl config set-context magnus-oec-dev --cluster=ctcnonprdusr101 --namespace=magnus-oec-dev --user=magnus-oec-dev
            kubectl config use-context magnus-oec-dev
 
            kubectl apply -f $GITHUB_WORKSPACE/k8s
            
            echo "${{ steps.date.outputs.image_tag}}"
            kubectl set image deployment/magnus-oec-api -n=magnus-oec-dev magnus-oec-api=${{needs.build.outputs.docker_image}}
          fi
 

  Deploy_to_Demo:
      runs-on: uhg-runner
      environment: Deploy_to_stage_prod
      needs: [build, Deploy_to_Dev]
      steps:
      - run: echo ${{needs.build.outputs.docker_image}}
      - uses: actions/checkout@v3
        with:
          ref: ${{needs.build.outputs.branch}}
      - uses: azure/setup-kubectl@v3
        with:
          version: latest
      - name: Set Kube Config
        shell: bash
        run: |
                mkdir ~/.kube
                pwd 
                if [ -n "${{ inputs.kube-config-file }}" ]; then
                  cp ${{ inputs.kube-config-file }} ~/.kube/config
                elif [ -n "${{ inputs.kube-config }}" ]; then
                  echo "${{ inputs.kube-config }}" > ~/.kube/config
                else
        
                  touch ~/.kube/config
                  export KUBECONFIG=~/.kube/config
                  echo "${{ secrets.K8S_CERTS }}" | base64 --decode > cert.crt
                  kubectl config set-credentials magnus-oec-dev --token="${{ secrets.K8S_TOKEN }}"
                  kubectl config set-cluster ctcnonprdusr101 --server=https://10.29.113.253:443 --certificate-authority=cert.crt
        
                  kubectl config set-context magnus-oec-dev --cluster=ctcnonprdusr101 --namespace=magnus-oec-dev --user=magnus-oec-dev
                  kubectl config use-context magnus-oec-dev
                  kubectl apply -f $GITHUB_WORKSPACE/k8s_demo
                  
                  echo "${{ steps.date.outputs.image_tag}}"
                  kubectl set image deployment/magnus-oec-api-demo -n=magnus-oec-dev magnus-oec-api-demo=${{needs.build.outputs.docker_image}}
                fi

  Promote-image:
      runs-on: uhg-runner
      environment: Deploy_to_stage_prod
      needs: [build, Deploy_to_Dev, Deploy_to_Demo]
      steps:
      - run: echo ${{needs.build.outputs.docker_image}}
             echo ${{needs.build.outputs.image_tag}}
      - name: promote image
        shell: bash
        run: |
              curl -u ${{ secrets.SVC_BUILD_USERID }}:${{ secrets.SVC_BUILD_PWD }} -X POST https://repo1.uhc.com/artifactory/api/docker/optum-docker/v2/promote -H "cache-control: no-cache" -H "content-type: application/json" -d "{\"targetRepo\" : \"optum-docker-prod\", \"dockerRepository\" : \"magnus-oec/magnus_oec_api\", \"tag\" : \"${{needs.build.outputs.image_tag}}\", \"copy\": true }"

  Deploy_to_PROD:            
      runs-on: uhg-runner
      environment: Deploy_to_stage_prod
      needs: [build, Deploy_to_Dev, Deploy_to_Demo, Promote-image]
      steps:
      - run: echo ${{needs.build.outputs.docker_image}}
      - uses: actions/checkout@v3
        with:
          ref: ${{needs.build.outputs.branch}}
      - uses: azure/setup-kubectl@v3
        with:
          version: latest
      - name: Set Kube Config
        shell: bash
        run: |
              mkdir ~/.kube
              pwd 
              if [ -n "${{ inputs.kube-config-file }}" ]; then
                cp ${{ inputs.kube-config-file }} ~/.kube/config
              elif [ -n "${{ inputs.kube-config }}" ]; then
                echo "${{ inputs.kube-config }}" > ~/.kube/config
              else
        
                touch ~/.kube/config
                export KUBECONFIG=~/.kube/config
                echo "${{ secrets.K8S_CERTS_PROD_CTC }}" | base64 --decode > cert.crt
                kubectl config set-credentials lingo-prod-ctc --token="${{ secrets.K8S_TOKEN_PROD_CTC }}"
                kubectl config set-cluster ctcprdusr201 --server=https://10.73.137.253:443 --certificate-authority=cert.crt
      
                kubectl config set-context lingo-prod-ctc --cluster=ctcprdusr201 --namespace=lingo-prod --user=lingo-prod-ctc
                kubectl config use-context lingo-prod-ctc
                kubectl kustomize $GITHUB_WORKSPACE/k8s_prod  > $GITHUB_WORKSPACE/k8s_prod/compiled.yml
                cat $GITHUB_WORKSPACE/k8s_prod/compiled.yml | sed 's/datacenter/ctc/g' | kubectl apply -f -
  
                # kubectl apply -f $GITHUB_WORKSPACE/prod
                kubectl set image deployment/lingo-api -n=lingo-prod lingo-api=${{needs.build.outputs.docker_image}}
  
                echo "${{ secrets.K8S_CERTS_PROD_ELR }}" | base64 --decode > cert.crt
                kubectl config set-credentials lingo-prod-elr --token="${{ secrets.K8S_TOKEN_PROD_ELR }}"
                kubectl config set-cluster elrprdusr201 --server=https://10.74.137.253:443 --certificate-authority=cert.crt
      
                kubectl config set-context lingo-prod-elr --cluster=elrprdusr201 --namespace=lingo-prod --user=lingo-prod-elr
                kubectl config use-context lingo-prod-elr
                kubectl kustomize $GITHUB_WORKSPACE/k8s_prod  > $GITHUB_WORKSPACE/k8s_prod/compiled.yml
                cat $GITHUB_WORKSPACE/k8s_prod/compiled.yml | sed 's/datacenter/elr/g' | kubectl apply -f -
  
                # kubectl apply -f $GITHUB_WORKSPACE/k8s_prod
                  
                echo "${{ needs.build.outputs.docker_image}}"
                kubectl set image deployment/lingo-api -n=lingo-prod lingo-api=${{needs.build.outputs.docker_image}}
              fi
