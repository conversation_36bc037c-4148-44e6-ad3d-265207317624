#!/usr/bin/env python3
"""
Test script to verify which model (Llama or OpenAI) is being used by the Surest plugin.
This script simulates a conversation with the Surest assistant to trigger model usage.
"""

import asyncio
import logging
from langchain_core.messages import HumanMessage, AIMessage
from client_plugins.surest.surest.main import surest_runnable

# Configure logging to show all info messages
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

async def test_surest_model():
    """Test function that sends a message to the Surest plugin to see which model responds"""
    print("\n====== TESTING SUREST MODEL USAGE ======")
    print("This test will show which model (Llama or OpenAI) is used by the Surest plugin")
    
    # Create a simple conversation with a message about Surest
    messages = [
        HumanMessage(content="Tell me about Surest plans")
    ]
    
    # Run the Surest plugin with this message
    print("\nSending message to Surest plugin...")
    response = await surest_runnable.ainvoke({"messages": messages})
    
    # Print the response
    print("\nResponse from Surest plugin:")
    print(f"Content: {response.content}")
    print("\nCheck the logs above to see if 🦙 LLAMA MODEL or 🔵 OPENAI MODEL was used\n")

if __name__ == "__main__":
    asyncio.run(test_surest_model())