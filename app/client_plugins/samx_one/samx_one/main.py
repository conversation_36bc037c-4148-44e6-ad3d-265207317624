from client_plugins.samx_one.samx_one.tool import create_structure_from_schema,validate_binder_check
from graph.state import State
from graph.nodes import Assistant
from config.openAI import model
from langgraph.graph import END, StateGraph, START
from langgraph.prebuilt import tools_condition

from langchain_core.runnables import Run<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from langgraph.prebuilt import ToolNode
from tools.common_tools import CompleteOrEscalate
from utils.helpers.constants import TOOL_CALL_ERROR_MESSAGE
from client_plugins.samx_one.samx_one.prompts import samx_one_system_prompt
# Import the other required modules


samx_one_tools = [create_structure_from_schema,validate_binder_check]  + [CompleteOrEscalate]
samx_one_tool_node = ToolNode(samx_one_tools, handle_tool_errors = TOOL_CALL_ERROR_MESSAGE)
samx_one_runnable = samx_one_system_prompt | model.bind_tools(samx_one_tools, parallel_tool_calls= False) 