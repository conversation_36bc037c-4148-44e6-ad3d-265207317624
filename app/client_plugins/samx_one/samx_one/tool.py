from langgraph.prebuilt.tool_node import InjectedState
from typing import Annotated
from langchain_core.tools import tool
import os
import httpx
from client_plugins.samx_one.samx_one_utils.utils import create_structure_from_xtractor_response, get_prior_carrier_list, update_schema_type_for_ny_surcharge
from config.db import samx_one_db
import base64
import io
import traceback
from utils.tool_utils import log_tool_error
from config.openAI import model
from utils.general_utils import get_gpt_message_object
from client_plugins.samx_one.samx_one.classes import BinderCheck
import json
                                    
json_structure_schemas_collection = samx_one_db['lingo_json_structure_schemas']

@tool
async def create_structure_from_schema(state: Annotated[dict, InjectedState], schema_type: list[str]):
    """Call this tool to create JSON from predefined PDF from schema using schema type without user uploading the pdf."""

    try:
        pdf_data = state.get("pdf_data")
        if not pdf_data:
            raise Exception("Please upload a relevant pdf file to get structured output.")
        
        pdf_data = base64.b64decode(pdf_data)   
        pdf_file = io.BytesIO(pdf_data)  
        pdf_file.filename = 'document.pdf'
        
        form_data = {
            'pdf_file': ('document.pdf', pdf_file, 'application/pdf')
        }

        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True', timeout=120.0) as client:
            response = await client.post(os.environ["XTRACTOR_ENDPOINT"], files=form_data)
            if response.status_code >= 400:
                raise Exception(f"Failed to process the PDF file. HTTP Status: {response.status_code}")
            
            response_json = response.json()

        if not response_json:
            raise Exception("Failed to process the PDF file. Please try again.")
        

        schema_type = update_schema_type_for_ny_surcharge(schema_type, response_json["content"])

        structure_schemas_cursor = json_structure_schemas_collection.find({'schema_type': {"$in": schema_type}})
        final_structure_schemas = []

        async for structure_schema in structure_schemas_cursor:
            for schema in structure_schema["schema"]:
                if structure_schema["schema_type"] == "express_automation_quote" and (schema["fieldname"] == "selectCarrier" or schema["fieldname"] == "otherCarrier"):
                    schema["values"] = await get_prior_carrier_list()

                schema["path"] = structure_schema["schema_type"] + "." + schema["path"]
                final_structure_schemas.append(schema)
        
        if len(final_structure_schemas) == 0:
            raise Exception("No schemas found for the provided schema type.")
 
        structured_response = await create_structure_from_xtractor_response(response_json["content"], response_json["key_value_pairs"], final_structure_schemas)
        
        return structured_response
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "An error occurred while processing the PDF file")
        raise Exception("An error occurred while processing the PDF file")

@tool
async def validate_binder_check(state: Annotated[dict, InjectedState]):
    """This tool is used to validate the binder-check, if the query consists of validate binder-check then this tool should be tagged with it.
    The pdf is present in state. no need to pass the pdf file as an argument."""

    try:
        pdf_data = state.get("pdf_data")
        if not pdf_data:
            raise Exception("Please upload a relevant pdf file to get structured output.")
        pdf_data = base64.b64decode(pdf_data)   

        prompt =f"""
            you are an expert in extracting data from binder-check images.
            here are the fields you need to extract from the binder-check image.
            1. company_name: The name of the company.
            2. signature: A boolean value indicating whether the document has a signature or not.
            3. check_number: A boolean value indicating if the check number exists.
            4. routing_number: A boolean value indicating if the routing number is nine digits.
            5. valid_binder_check: A boolean value indicating whether the document is a valid binder check or not.
            6. reason: If the document is not a valid binder check, please provide the reason for it.
        """
        messages = await get_gpt_message_object(pdf_data,prompt,200,True)
        llm_str = model.with_structured_output(BinderCheck)

        response = await llm_str.ainvoke(messages)
        if not isinstance(response, BinderCheck):
            raise Exception("An error occurred while processing the PDF file")

        # Convert the response to a dictionary if it's a BinderCheck object
        response = response.__dict__

        json_response = json.dumps(response)
        return json_response
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "An error occurred while processing the PDF file")
        raise Exception("An error occurred while processing the PDF file")