from config.openAI import json_model
from config.db import samx_one_db
import json

prior_carriers_collection = samx_one_db['mst_priorCarriers']

async def create_structure_from_xtractor_response(content, key_value_pairs, schema):
    prompt = f"""
        System: You are an expert in encoding data from documents for creating health insurance group.
        You have been given a task to extract structured data from content and key_value_pair.
        You'll be given two pieces of data extracted from document and one schema.

        1. schema: List of fields with their descriptions and paths and few fields have rules and data_type. This is the blueprint for the data you need to extract. Each field in the schema is a dictionary with the following keys:
            fieldname: The name of the field that will be added to the JSON.
            description: A brief description of the field that will be used for extracting the data for that field.
            path: The path where the field will be added in the JSON.
            rules: Any specific rules or conditions that apply to the field.
            data_type: The data type of the field (e.g., string, number, date).

        Always follow the schema to create the JSON.

        extract the values for the specified fields from the content and key_value_pair and return them in JSON format. Here are example the fields:
        [
            {{
                "fieldname": "companyName",
                "description": "This is the company name, also termed as business name or full legal business name.",
                "path": "companyProfile.companyName"
            }},
            {{
                "fieldname": "effectiveDate",
                "description": "This is the effective date when the company profile becomes active.",
                "path": "companyProfile.effectiveDate"
            }},
            {{
                "fieldname": "zipCode",
                "description": "This is the ZIP code of the company's location.",
                "path": "companyProfile.zipCode"
            }},
            {{
                "fieldname": "state",
                "description": "This is the state where the company is located.",
                "path": "companyProfile.state"
            }}
        ]
        The output will be like this:   
        {{
            "companyProfile": {{
                "companyName": "Extracted Company Name",
                "effectiveDate": "Extracted Effective Date",
                "zipCode": "Extracted ZIP Code",
                "state": "Extracted State"
            }}
        }}

        2. content: The whole text extracted from the documents.
        3. key_value_pairs: list of dictionaries, each containing a "key" and "value" extracted from the document.

        Instructions:
        1. For each field in the 'schema', find the corresponding value in the 'key_value_pairs'.
        2. If a value is not found in the 'key_value_pairs', search for it in the 'content'.
        3. Construct a dictionary with the keys from the 'schema' and the corresponding values from the 'key_value_pairs' or 'content'.
        4. Extract as many fields as possible from the provided data and if any value is not found, keep it empty string.
        5. Strict Instruction. Do not miss any field and information obtained from content and key_value_pairs for which schema exists.
        6. Do not hallucinate. Do not change the 'schema'. Always follow the path mentioned in the 'schema'. 
        
        schema : {schema}
        content : {content}
        key_value_pairs : {key_value_pairs}

        Please provide the structured data as a JSON object.
    """
    response = await json_model.ainvoke(prompt)
    json_response = json.loads(response.content)
    return json_response

async def get_prior_carrier_list():
    """Fetch the list of prior carriers from the database."""
    try:
        prior_carriers_cursor = prior_carriers_collection.find({}, {"_id": 0, "CarrierDescription": 1})
        prior_carriers = []
        async for carrier in prior_carriers_cursor:
            prior_carriers.append(carrier["CarrierDescription"])
        return prior_carriers
    except Exception as e:
        raise ValueError("An error occurred while fetching prior carriers") from e
    
def update_schema_type_for_ny_surcharge(schema_type: list[str], content: list[str]) -> list[str]:
    if 'new_york_surcharge_form_or_applicable_waiver' in schema_type:
        schema_type.remove('new_york_surcharge_form_or_applicable_waiver')

        content_mapping = {
            'DOH-4403': 'ny_surcharge_4403',
            'DOH -4403': 'ny_surcharge_4403',
            'DOH-4264': 'ny_surcharge_4264',
            'DOH -4264': 'ny_surcharge_4264',
            'DOH-4399': 'ny_surcharge_4399',
            'DOH -4399': 'ny_surcharge_4399',
            'Non-Participation Election Form': 'non-participation_election_form',
            'Non-Participation': 'non-participation_election_form'
        }

        for content in content:
            for key, value in content_mapping.items():
                if key in content and value not in schema_type:
                    schema_type.append(value)
    
    return schema_type
