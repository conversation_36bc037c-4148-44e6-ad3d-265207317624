import os
from difflib import <PERSON><PERSON><PERSON>atcher
import httpx
from routes.gco import fetch_gco_reports_data
from graph.state import State
from datetime import datetime
from routes.gco import store_user_tracking_info

def fuzzy_match_group(user_input: str, group_list: list) -> list:
    """
    Fuzzy matches user input with the group list and suggests the top 5 matches.

    Args:
        user_input (str): The unstructured input from the user.
        group_list (list): The list of group names.

    Returns:
        list: The top 5 matches from the group list or a message if no match is found.
    """
    user_input = user_input.lower()
    matches = []

    for group in group_list:
        group_lower = group.lower()
        # Check if user input is a substring of the group name
        if user_input in group_lower:
            matches.append((group, 100))  # Perfect match
            continue
        # Check for abbreviation matches (e.g., "CVC" for "CURTIS V COOPER")
        abbreviation = ''.join(word[0] for word in group.split() if word[0].isalnum()).lower()
        if user_input in abbreviation:
            matches.append((group, 90))  # High confidence for abbreviation match
            continue
        # Use SequenceMatcher for a more robust similarity check
        score = SequenceMatcher(None, user_input, group_lower).ratio()
        if score >= 0.5:  # Lower cutoff to handle minor spelling errors
            matches.append((group, score))

    # Sort matches by score in descending order and return the top 5
    if matches:
        matches = sorted(matches, key=lambda x: x[1], reverse=True)[:5]
        return [{"value": match[0]} for match in matches]
    else:
        # If no match is found, split the user input into subwords and find matches
        subword_matches = []
        for i in range(2, len(user_input) + 1):  # Limit minimum split length to 2
            subword = user_input[i - 2:i]  # Increase the split starting point by 1 after each split
            #print(f"Checking subword: {subword}")  # Debugging line
            for group in group_list:
                group_lower = group.lower()
                # Check if subword is a substring of the group name
                if subword in group_lower:
                    subword_matches.append((group, 100))  # Perfect match
                    continue
                # Check for abbreviation matches (e.g., "CVC" for "CURTIS V COOPER" or "4R" for "4R ENVIRONMENTAL")
                abbreviation = ''.join(word[0] for word in group.split() if word[0].isalnum()).lower()
                if subword in abbreviation:
                    subword_matches.append((group, 90))  # High confidence for abbreviation match
                    continue
                # Use SequenceMatcher for a more robust similarity check
                score = SequenceMatcher(None, subword, group_lower).ratio()
                if score >= 0.5:  # Lower cutoff for subword matching
                    subword_matches.append((group, score))
        # Sort subword matches by score and return the top 5 unique matches
        subword_matches = sorted(subword_matches, key=lambda x: x[1], reverse=True)
        unique_matches = list(dict.fromkeys([match[0] for match in subword_matches]))[:5]
        #print("There might be spelling mistakes in your input. Here are some close matches:")
        return [{"value": match} for match in unique_matches] if unique_matches else []

def fetch_group_list(gco_report):
    """
    Fetches the list of groups from the gco_report JSON file.
    Returns a list of dictionaries with group names and their report counts.
    """
    if gco_report is None:
        raise Exception("gco_report is None. Please provide a valid gco_report JSON file.")
    group_list = []
    for entry in gco_report:
        if entry.get("group_name"):
            count_reports = len(entry.get("reports", []))
            group_list.append({"value": entry.get("group_name"), "type": 'group', "notes": str(count_reports)+ " Reports"})
            # Ensure group_list contains unique group names
        # Remove duplicates based on group_name
        seen = set()
        unique_group_list = []
        for group in group_list:
            if group["value"] not in seen:
                unique_group_list.append(group)
                seen.add(group["value"])
        group_list = unique_group_list
    return sorted(group_list, key=lambda x: x["value"].lower())

def fetch_report_type_list(gco_report,group: str):
    """
    Fetches the list of report types for a given group from the gco_report JSON file.
    Returns a list of dictionaries with report type names and their descriptions.
    """
    report_type_list = []
    for entry in gco_report:
        if entry.get("group_name") == group:
            for report in entry.get("reports", []):
                report_data = {
                    "value": report.get("report_type"),
                    "notes": report.get("report_desc"),
                    "type": "report_type",
                }
                if not any(item["value"] == report_data["value"] for item in report_type_list):
                    report_type_list.append(report_data)
    # Remove duplicates from report_type_list based on the "value" key
    seen = set()
    unique_report_type_list = []
    for report_type in report_type_list:
        if report_type["value"] not in seen:
            unique_report_type_list.append(report_type)
            seen.add(report_type["value"])
    report_type_list = unique_report_type_list
    return sorted(report_type_list, key=lambda x: x["value"].lower())

def fetch_date_list(gco_report, group: str, report_type: str):
    """
    Fetches the list of report dates for a given group and report type from the gco_report JSON file.
    Returns a list of dictionaries with report dates and their recent status.
    """
    report_dates_list = []
    for entry in gco_report:
        if entry.get("group_name") == group:
            for report in entry.get("reports", []):
                if report.get("report_type") == report_type:
                    report_data = {
                        "value": report.get("published_at"),
                        "isrecent": 0,
                        "type": "date",
                    }
                    if not any(item["value"] == report_data["value"] for item in report_dates_list):
                        report_dates_list.append(report_data)
    print("report_dates_list", report_dates_list)
    # Sort by date and mark the most recent one
    report_dates_list.sort(key=lambda x: x["value"], reverse=True)
    if len(report_dates_list) > 1:
        report_dates_list[0]["isrecent"] = 1
    # Remove duplicates from report_dates_list based on the "value" key
    seen_dates = set()
    unique_report_dates_list = []
    for report_date in report_dates_list:
        if report_date["value"] not in seen_dates:
            unique_report_dates_list.append(report_date)
            seen_dates.add(report_date["value"])
    report_dates_list = unique_report_dates_list
    return report_dates_list

def fetch_report_link(gco_report, group: str, report_type: str, publish_date: str):
    """
    Fetches the report link for a given group, report type, and date from the gco_report JSON file.
    Returns a dictionary with the report link and its type.
    """
    report_out = []
    for entry in gco_report:
        if entry.get("group_name") == group:
            for report in entry.get("reports", []):
                if (
                    report.get("report_type") == report_type
                    and report.get("published_at") == publish_date
                ):
                    report_link = report.get("report_link")
                    file_name = report.get("file_name")
                    platform_id = report.get("platform_id", "")
                    funding_type = report.get("funding_type", "")
                    if report_link:
                        report_details = [report_link, file_name, platform_id, funding_type]
                        report_out.append(report_details)
    return report_out 




def get_downloaded_report_details(gco_report, group: str = None, report_type: str = None, num_reports: int = 5):
    """
    Fetches a list of report details sorted by the count of downloads in descending order based on the provided filters (group and report type).
    Only includes reports where the download count is greater than 0.

    Args:
        gco_report (list): The JSON data containing report information.
        group (str, optional): The group name to filter reports. Defaults to None.
        report_type (str, optional): The type of report to filter. Defaults to None.
        num_reports (int, optional): The number of reports to return. Defaults to 10.

    Returns:
        list: A list of dictionaries containing report details matching the criteria.
    """
    report_details = []

    for report in gco_report:
        if group and report.get("group_name", "") != group:
            continue  # Skip if group filter is provided and doesn't match

        if (
            (not report_type or report.get("report_type", "") == report_type)  # Match report_type if provided
            and report.get("count", 0) > 0  # Include only reports with download count > 0
        ):
            report_details.append({
                "group_id": report.get("group_id", ""),
                "group_name": report.get("group_name", ""),
                "user_name": report.get("user_name", ""),
                "user_email": report.get("user_email", ""),
                "file_name": report.get("file_name", ""),
                "report_type": report.get("report_type", ""),
                "published_at": report.get("published_at", ""),
                "platform_id": report.get("platform_id", ""),
                "report_link": report.get("report_link", ""),
                "funding_type": report.get("funding_type", ""),
                "download_count": report.get("count", 0)  # Fetch download count
            })

    # Sort reports by download count in descending order and limit to num_reports
    sorted_reports = sorted(report_details, key=lambda x: x["download_count"], reverse=True)
    return sorted_reports[:num_reports] if sorted_reports else None

def parse_timestamp(timestamp_str: str):
    """
    Parses a timestamp string into a datetime object, supporting multiple formats.

    Args:
        timestamp_str (str): The timestamp string to parse.

    Returns:
        datetime: The parsed datetime object.
    """
    timestamp_formats = ["%a, %d %b %Y %H:%M:%S %Z", "%d/%m/%Y %H:%M:%S", "%m/%d/%Y %H:%M:%S"]
    for fmt in timestamp_formats:
        try:
            return datetime.strptime(timestamp_str, fmt)
        except ValueError:
            continue
    raise ValueError(f"Timestamp '{timestamp_str}' does not match any supported formats.")

def get_recent_report_details(gco_report, group: str = None, report_type: str = None, num_reports: int = None):
    """
    Fetches a list of report details sorted by timestamp in ascending order based on the provided filters (group and report type).
    Only includes reports where download_count > 0 and limits the number of reports if specified.

    Args:
        gco_report (list): The JSON data containing report information.
        group (str, optional): The group name to filter reports. Defaults to None.
        report_type (str, optional): The type of report to filter. Defaults to None.
        num_reports (int, optional): The number of reports to return. Defaults to None (returns all matching reports).

    Returns:
        list: A list of dictionaries containing report details matching the criteria.
    """
    report_details = []

    for report in gco_report:
        if group and report.get("group_name", "") != group:
            continue  # Skip if group filter is provided and doesn't match

        if not report_type or report.get("report_type", "") == report_type:  # Match report_type if provided
            try:
                timestamp = parse_timestamp(report.get("downloaded_on", ""))
                report_details.append({
                    "group_id": report.get("group_id", ""),
                    "group_name": report.get("group_name", ""),
                    "user_name": report.get("user_name", ""),
                    "user_email": report.get("user_email", ""),
                    "file_name": report.get("file_name", ""),
                    "report_type": report.get("report_type", ""),
                    "published_at": report.get("published_at", ""),
                    "platform_id": report.get("platform_id", ""),
                    "report_link": report.get("download_link", ""),
                    "funding_type": report.get("funding_type", ""),
                    "updated_timestamp": timestamp  # Parsed timestamp
                })
            except ValueError:
                print(f"Skipping report with invalid timestamp format: {report.get('downloaded_on')}")

    # Sort reports by timestamp in descending order
    sorted_reports = sorted(report_details, key=lambda x: x["updated_timestamp"], reverse=True)

    # Limit the number of reports if num_reports is specified
    return sorted_reports[:num_reports] if num_reports else sorted_reports

def get_not_downloaded_report_details(gco_report, download_data, group: str = None, report_type: str = None):
    """
    Fetches report details from gco_report where the link is not present in download.json.

    Args:
        gco_report (list): The JSON data containing report information.
        download_data (list): The JSON data containing downloaded report links.
        group (str, optional): The group name to filter reports. Defaults to None.
        report_type (str, optional): The report type to filter reports. Defaults to None.
        num_reports (int, optional): The number of reports to return. Defaults to None (returns all matching reports).

    Returns:
        list: A list of dictionaries containing report details matching the criteria.
    """
    downloaded_links = {entry.get("_id") for entry in download_data}
    report_details = []

    for entry in gco_report:
        if group and entry.get("group_name") != group:
            continue  # Skip if group filter is provided and doesn't match

        for report in entry.get("reports", []):
            if (
                (not report_type or report.get("report_type") == report_type)  # Match report_type if provided
                and report.get("report_link") not in downloaded_links  # Exclude reports already downloaded
            ):
                report_details.append({
                    "group_id": entry.get("group_id", ""),
                    "group_name": entry.get("group_name", ""),
                    "user_name": entry.get("user_name", ""),
                    "user_email": entry.get("user_email", ""),
                    "file_name": report.get("file_name", ""),
                    "report_type": report.get("report_type", ""),
                    "published_at": report.get("published_at", ""),
                    "platform_id": report.get("platform_id", ""),
                    "report_link": report.get("report_link", ""),
                    "funding_type": report.get("funding_type", ""),
                })

    
    return report_details


async def get_redis(state: State, uuid:str):
    """
    Fetches the Redis client instance using the provided UUID.
    
    Args:
        uuid (str): The UUID to fetch the Redis client for.
    
    Returns:
        Redis client instance or None if not found.
    """
    try:
        redis_url = os.environ.get('GCO_REDIS_URL') + 'get'
        payload = {
                        "key": "GCO:" + uuid,
                    }
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.post(redis_url, json=payload)
            if response.status_code != 200:
                if state.get('additional_arg', {}).get("bne_user_type") == 'Internal':
                    # recored Internal user Logging in action
                    store_user_tracking_info(uuid, "Login", 'Internal', state.get("user_info", {}))
                    internal_user_data = await fetch_gco_reports_data(uuid, 'INTERNAL')
                    return internal_user_data.get('response')
            return response.json() 
    except Exception as e:
        print(f"Error fetching Redis client: {e}")
        return None




def parse_date(date_str: str):
    """
    Parses a date string into a datetime object, supporting multiple formats.

    Args:
        date_str (str): The date string to parse.

    Returns:
        datetime: The parsed datetime object.
    """
    date_formats = ["%Y-%m-%d", "%d/%m/%Y", "%m/%d/%Y"]
    for fmt in date_formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue
    raise ValueError(f"Date '{date_str}' does not match any supported formats.")    
 
def get_published_report_details(gco_report, group: str = None, report_type: str = None, last_login: str = None):
    """
    Fetches a list of report details based on the provided filters (group, report type, and last login).
    If only last login is provided, fetches all reports published after the last login date.

    Args:
        gco_report (list): The JSON data containing report information.
        group (str, optional): The group name to filter reports. Defaults to None.
        report_type (str, optional): The type of report to filter. Defaults to None.
        last_login (str, optional): The last login date in string format. Defaults to None.

    Returns:
        list: A list of dictionaries containing report details matching the criteria.
    """
    last_login_date = last_login.get("last_login_date")

    last_login = f"{last_login_date}"
    last_login = parse_date(last_login)
    if not last_login:
        raise ValueError("The 'last_login' parameter is required.")

    report_details = []
    print("inside get_published_report_details:", last_login)

    for entry in gco_report:
        if group and entry.get("group_name") != group:
            continue  # Skip if group filter is provided and doesn't match

        for report in entry.get("reports", []):
            published_at = report.get("published_at")
            published_at = parse_date(published_at)
            if (
                published_at
                and published_at > last_login  # Compare parsed dates
                and (not report_type or report.get("report_type") == report_type)  # Match report_type if provided
            ):
                report_details.append({
                    "group_id": entry.get("group_id", ""),
                    "group_name": entry.get("group_name", ""),
                    "user_name": entry.get("user_name", ""),
                    "user_email": entry.get("user_email", ""),
                    "file_name": report.get("file_name", ""),
                    "report_type": report.get("report_type", ""),
                    "published_at": report.get("published_at", ""),
                    "platform_id": report.get("platform_id", ""),
                    "report_link": report.get("report_link", ""),
                    "funding_type": report.get("funding_type", ""),
                })

    return report_details if report_details else None

def keywords_cer_ticket_feedback(query: str):
    """
    Checks if the query contains keywords related to CER, ticket, or feedback.
    Args:
        query (str): The user query to check for keywords.
    
    Returns:
        str: Returns 'cer' if CER-related keywords are found, 'ticket' if ticket-related keywords are found,
             'feedback' if feedback-related keywords are found, or None if no keywords match.
    """
    keywords_cer = ["pvc vs claims", "pvc versus claims", "pvc and claims", "claims vs pvc", "Claim Lag Study", "Claims Expenses by Size of Payment", "Cost and Utilization by Procedure",
        "Cost and Utilization Summary Report", "Detail Payment Report", "Distribution of Discounts", "Distribution of Ineligible Charges", "Distribution of Other Savings",
        "Health Care Cost Management Summary", "Inpatient Utilization And Cost by Admission Type", "Inpatient Utilization by Diagnosis", "Key Generic Substitution Indicators by Month",
        "Large Loss Claim Payments (eCR Financial Report)", "Managed Pharmacy Cost and Utilization by Month", "Managed Pharmacy Critical Indicators", "Managed Pharmacy Utilization by Age Group", "Membership by Market",
        "Membership by Month", "Membership with Demographic and Geographic Factor", "Network Utilization", "Outpatient Utilization by Diagnosis", "Payments by Benefit Type",
        "Payments by Month", "Premium vs Claim - Summary", "Top Drug Utilization Ranked by Total Net Paid", "Top Drug Utilization Ranked by Volume", "Top Hospitals Ranked-Total Net Paid",
        "Top Physicians Ranked-Total Net Paid", "Top Therapeutic Class Utilization Ranked by Total Net Paid", "Top Therapeutic Class Utilization Ranked by Volume",
        "Utilization And Cost by Provider Type", "Utilization by Age Group", "Utilization by Diagnosis"]
    keywords_ticket = ["submit ticket", "ticket", "ticket submit"]
    keywords_feedback = ["submit feedback", "feedback", "feedback submit"]
    if any(keyword in query.lower() for keyword in keywords_cer):
        return "cer"
    elif any(keyword in query.lower() for keyword in keywords_ticket):
        return "ticket"
    elif any(keyword in query.lower() for keyword in keywords_feedback):
        return "feedback"
    else:
        return None
   

async def get_rdr(user_id: str):
    """
    Fetches data from the RDR API for the given user ID.

    Args:
        user_id (str): The user ID to fetch data for.

    Returns:
        dict: The JSON response from the API or None if an error occurs.
    """
    try:
        base_url = os.environ.get('GCO_PARSER_URL')
        api_url = f"{base_url}/getusertrackinginfoforrdr/{user_id}"
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.get(api_url)
            response.raise_for_status()  # Raise an exception for HTTP errors
            return response.json()
    except Exception as e:
        print(f"Error fetching data from RDR API: {e}")
        return None


async def get_mdr(user_id: str):
    """
    Fetches data from the MDR API for the given user ID.

    Args:
        user_id (str): The user ID to fetch data for.

    Returns:
        dict: The JSON response from the API or None if an error occurs.
    """
    try:
        base_url = os.environ.get('GCO_PARSER_URL')
        api_url = f"{base_url}/getusertrackinginfoformdr/{user_id}"
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.get(api_url)
            response.raise_for_status()  # Raise an exception for HTTP errors
            return response.json()
    except Exception as e:
        print(f"Error fetching data from MDR API: {e}")
        return None


async def get_ndr(user_id: str):
    """
    Fetches data from the NDR API for the given user ID.

    Args:
        user_id (str): The user ID to fetch data for.

    Returns:
        dict: The JSON response from the API or None if an error occurs.
    """
    try:
        base_url = os.environ.get('GCO_PARSER_URL')
        api_url = f"{base_url}/getusertrackinginfoforndr/{user_id}"
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.get(api_url)
            response.raise_for_status()  # Raise an exception for HTTP errors
            return response.json()
    except Exception as e:
        print(f"Error fetching data from NDR API: {e}")
        return None


async def get_npr(user_id: str):
    """
    Fetches data from the NPR API for the given user ID.

    Args:
        user_id (str): The user ID to fetch data for.

    Returns:
        dict: The JSON response from the API or None if an error occurs.
    """
    try:
        base_url = os.environ.get('GCO_PARSER_URL')
        api_url = f"{base_url}/getusertrackinginfofornpr/{user_id}"
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.get(api_url)
            response.raise_for_status()  # Raise an exception for HTTP errors
            return response.json()
    except Exception as e:
        print(f"Error fetching data from NPR API: {e}")
        return None
