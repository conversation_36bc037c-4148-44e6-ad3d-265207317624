from langchain_core.tools import tool
from langgraph.prebuilt.tool_node import InjectedState
from typing import Annotated
from langgraph.prebuilt.tool_node import InjectedState  
import traceback
from utils.tool_utils import log_tool_error
from client_plugins.gco_reporting.gco_reporting_utils.utils import (
    fetch_group_list,
    fetch_report_type_list,
    fetch_date_list,
    fuzzy_match_group,
    fetch_report_link,
    get_redis,
    get_rdr,
    get_mdr,
    get_ndr,
    get_npr,
    get_published_report_details,
    get_downloaded_report_details,
    get_recent_report_details,
    get_not_downloaded_report_details,
    keywords_cer_ticket_feedback
)
from routes.gco import store_user_tracking_info
import os
import requests

@tool
async def get_group_list(state: Annotated[dict, InjectedState]):
    """
    Having capability to get the list of groups from the gco report which is json file.
    This tool is used only when user ask for group list.
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """
    try:
        uuid = state.get("user_info", {}).get("uuid")
        gco_report = await get_redis(state, uuid)
        if not gco_report:
            return "Sorry, I couldn't find any groups. Please check the data and try again."
        
        group_list = fetch_group_list(gco_report)

        if not group_list:
            return "Sorry, I couldn't find any groups. Please check the data and try again."

        return {
            "answer": "Here’s a full list of available groups. Which group would you like to select?",
            "custom_suggestions": group_list
        }
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "get_group_list")
        traceback.print_exc()

@tool
async def get_report(state: Annotated[dict, InjectedState], group: str):
    """
    Having capability to get the Report Types provided group in the gco report which is json file.
    This is the question asked from the user after selecting the group."
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """
    try:  
        uuid = state.get("user_info", {}).get("uuid")
        gco_report = await get_redis(state, uuid)
        group_list = fetch_group_list(gco_report)
        all_groups = [item["value"] for item in group_list]
        matched_groups = [grp for grp in all_groups if grp.lower().startswith(group.lower())]
        if not matched_groups:
            matched_groups = fuzzy_match_group(group, all_groups)
        if len(matched_groups) == 1:
            group = matched_groups[0]
        elif len(matched_groups) > 1:
            return {
                "answer": f"Did you mean one of these groups?",
                "custom_suggestions": matched_groups
            }
        else:
            return {
                "answer":f"Sorry, I couldn't find any group similar to '{group}'. Available groups list are",
                "custom_suggestions": group_list
            }
        
        report_type_list = fetch_report_type_list(gco_report, group)

        if not report_type_list:
            return {
                "answer":f"Sorry, I couldn't find any report for group similar to '{group}'. Please check group name. Available groups list are",
                "custom_suggestions": group_list
            }
        
        return {
                "answer": f"Ok. Here’s a list of available report types for {group}. What report type would you like to download?",
                "custom_suggestions": report_type_list
            }
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "get_report")
        traceback.format_exc()

@tool
async def get_date(state: Annotated[dict, InjectedState], group: str, report_type: str):
    """
    Having capability to get the report date provided Report Types in the gco report which is json file. 
    This is the question asked from the user after selecting the Report Types.
    If user give date at the starting ask user to select group and report_type.
    If all required information like group and report_type is provided by the user, use this tool directly without asking for further clarification or suggestions. Proceed to fetch and return the report_dates_list immediately.
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """
    try:
        uuid = state.get("user_info", {}).get("uuid")
        gco_report = await get_redis(state, uuid)
        group_list = fetch_group_list(gco_report)
        all_groups = [item["value"] for item in group_list]      
        matched_groups = [grp for grp in all_groups if grp.lower().startswith(group.lower())]
        if not matched_groups:
            matched_groups = fuzzy_match_group(group, all_groups)
        if len(matched_groups) == 1:
            group = matched_groups[0]
        elif len(matched_groups) > 1:
            print(matched_groups)
            return {
                "answer": f"Did you mean one of these groups?",
                "custom_suggestions": matched_groups
            }
        else:
            return {
                "answer":f"Sorry, I couldn't find any group similar to '{group}'. Available groups list are",
                "custom_suggestions": group_list
            }
        report_list = fetch_report_type_list(gco_report, group)
        all_report = [item["value"] for item in report_list]
        matched_report = [rep for rep in all_report if rep.lower().startswith(report_type.lower())]
        if not matched_report:
            matched_report = fuzzy_match_group(report_type, all_report)
        if len(matched_report) == 1:
            report_type = matched_report[0]
        elif len(matched_report) > 1:
            print(matched_report)
            return {
                "answer": f"Did you mean one of these reports?",
                "custom_suggestions": matched_report
            }
        else:
            return {
                "answer":f"Sorry, I couldn't find any report similar to '{report_type}'. Available report list are",
                "custom_suggestions": report_list
            }
        report_dates_list = fetch_date_list(gco_report, group, report_type)
        if not report_dates_list:
            return  {
                "answer": f"Sorry, I couldn't find any reports for group {group} having report type {report_type}. Please check the report type and try again. Available report types are:",
                "custom_suggestions": report_list
            }
        else:
            return {
                   "answer": f"Sounds good! I found multiple published {report_type} reports. What published date would you like to see the report for?",
                    "custom_suggestions": report_dates_list
                }
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "get_date")
        traceback.format_exc()

@tool
async def get_link(state: Annotated[dict, InjectedState], group:str, report_type: str, published_date: str):
    """
    Having capability to get the report link provided group, report_types and report_dates in the gco_report which is json file. 
    This is the information given to user about URL for selected group, report_type and published_date by the user.
    If all required information like group, report_type, and published_date is provided by the user, use this tool directly without asking for further clarification or suggestions. Proceed to fetch and return the report link immediately.
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """

    try:
        uuid = state.get("user_info", {}).get("uuid")
        gco_report = await get_redis(state, uuid)
        group_list = fetch_group_list(gco_report)
        all_groups = [item["value"] for item in group_list]
        matched_groups = [grp for grp in all_groups if grp.lower().startswith(group.lower())]
        if not matched_groups:
            matched_groups = fuzzy_match_group(group, all_groups)
        if len(matched_groups) == 1:
            group = matched_groups[0]
        elif len(matched_groups) > 1:
            return {
                "answer": f"Did you mean one of these groups?",
                "custom_suggestions": matched_groups
            }
        else:
            return {
                "answer":f"Sorry, I couldn't find any group similar to '{group}'. Available groups list are",
                "custom_suggestions": group_list
            }
        report_list = fetch_report_type_list(gco_report, group)
        all_report = [item["value"] for item in report_list]
        matched_report = [rep for rep in all_report if rep.lower().startswith(report_type.lower())]
        if not matched_report:
            matched_report = fuzzy_match_group(report_type, all_report)
        if len(matched_report) == 1:
            report_type = matched_report[0]
        elif len(matched_report) > 1:
            return {
                "answer": f"Did you mean one of these groups?",
                "custom_suggestions": matched_report
            }
        else:
            return {
                "answer":f"Sorry, I couldn't find any report similar to '{report_type}'. Available report list are",
                "custom_suggestions": report_list
            }
        report_details = fetch_report_link(gco_report, group, report_type, published_date)
    
        if len(report_details) == 0:
            published_date_list = fetch_date_list(gco_report, group, report_type)
            return {
                "answer": f"Sorry, I couldn't find a report link for {group} with {report_type} dated {published_date}. Please check report date. Avaliable report dates are:",
                "custom_suggestions": published_date_list
            }
        # Find the group_id corresponding to the selected group and report_link
        suggestion = []
        for entry in gco_report:
            if entry.get("group_name") == group:
                group_id = entry.get("group_id")
                break
        for entry in report_details:
            report_link = entry[0]
            file_name = entry[1]
            platform_id = entry[2]
            funding_type = entry[3]

            body = {
                "user_id": state.get("user_info", {}).get("uuid"),
                "user_type": "external",
                "user_access_role": "Key Accounts & National Accounts",
                "user_ext_type": "Broker",
                "action_type": "download",
                "incident_number": "",
                "group_id": group_id,
                "group_name": group,
                "user_name": "",
                "user_email": "",
                "file_name": file_name,
                "report_type": report_type,
                "published_at": published_date,
                "platform_id": platform_id,
                "funding_type": funding_type,
                "session_id": ""    # is needed for tracking purposes?
            }
            suggestion.append({
                "file_url": report_link,
                "file_date": published_date,
                "report_type": report_type,
                "type": "download",
                "group": group,
                "details": "",
                "download_info": body
            })
        if not suggestion:
            return {
                "answer": f"Sorry, I couldn't find a report link for {group} with {report_type} dated {published_date}. Please check report date.",
                "custom_suggestions": published_date_list
            }

        return {
            "answer": f"Here's the report for you to download.",
            "custom_suggestions": suggestion
        }
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "get_link")
        traceback.format_exc()

@tool
async def get_new_published_reports(state: Annotated[dict, InjectedState], group: str = None, report_type: str = None):
    """
    This tool provides the capability to get the latest published date for a given group and report type.
    It is used when the user asks for the latest published date.
    This tool can be called at the very beginning of the conversation. The user may or may not provide details such as group name, report type, or date. Always call this tool if the user asks for new, latest, or recently published reports, regardless of how much information the user provides.
    For example user query is "For last login {date} show me new published reports for all group", for some specific last login date, then call this tool.
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """
    try:
        uuid = state.get("user_info", {}).get("uuid")
        gco_report = await get_redis(state, uuid)
        last_login = await get_npr("Demo User-570425")
        if not gco_report:
            return {
                "answer": "Sorry, I couldn't find any reports. Please check the data and try again."
            }

        report_details = get_published_report_details(gco_report, group=group, report_type=report_type, last_login=last_login)
        if not report_details:
            return {
                "answer": f"Sorry, I couldn't find any new reports published after last login for the specified filters."
            }

        custom_suggestions_list = []
        for report in report_details:
            body = {
                "user_id": state.get("user_info", {}).get("uuid"),
                "user_type": "external",
                "user_access_role": "Key Accounts & National Accounts",
                "user_ext_type": "Broker",
                "action_type": "download",
                "incident_number": "",
                "group_id": report.get("group_id", ""),
                "group_name": report.get("group_name", ""),
                "user_name": report.get("user_name", ""),
                "user_email": report.get("user_email", ""),
                "file_name": report.get("file_name", ""),
                "report_type": report.get("report_type", ""),
                "published_at": report.get("published_at", ""),
                "platform_id": report.get("platform_id", ""),
                "funding_type": report.get("funding_type", ""),
                "session_id": ""  # is needed for tracking purposes?
            }

            custom_suggestions = {
                "file_url": report.get("report_link", ""),
                "file_date": report.get("published_at", ""),
                "report_type": report.get("report_type", ""),
                "type": "download",
                "group": report.get("group_name", ""),
                "details": "",
                "download_info": body
            }
            custom_suggestions_list.append(custom_suggestions)

        return {
            "answer": f"Here are the new reports published after last login. Which one would you like to download?",
            "custom_suggestions": custom_suggestions_list
        }
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "get_new_published_reports")
        traceback.format_exc()


@tool
async def cer_ticket_feedback_links(state: Annotated[dict, InjectedState], query: str):                         
    """
    This tool should be called whenever the user's query is related to CER reports, submitting a ticket, or submitting feedback. 
    For CER-related queries, use this tool if the query contains any of the following keywords: 
    ["pvc vs claims", "pvc versus claims", "pvc and claims", "claims vs pvc", "Claim Lag Study", "Claims Expenses by Size of Payment", "Cost and Utilization by Procedure",
    "Cost and Utilization Summary Report", "Detail Payment Report", "Distribution of Discounts", "Distribution of Ineligible Charges", "Distribution of Other Savings",
    "Health Care Cost Management Summary", "Inpatient Utilization And Cost by Admission Type", "Inpatient Utilization by Diagnosis", "Key Generic Substitution Indicators by Month",
    "Large Loss Claim Payments (eCR Financial Report)", "Managed Pharmacy Cost and Utilization by Month", "Managed Pharmacy Critical Indicators", "Managed Pharmacy Utilization by Age Group", "Membership by Market",
    "Membership by Month", "Membership with Demographic and Geographic Factor", "Network Utilization", "Outpatient Utilization by Diagnosis", "Payments by Benefit Type",
    "Payments by Month", "Premium vs Claim - Summary", "Top Drug Utilization Ranked by Total Net Paid", "Top Drug Utilization Ranked by Volume", "Top Hospitals Ranked-Total Net Paid",
    "Top Physicians Ranked-Total Net Paid", "Top Therapeutic Class Utilization Ranked by Total Net Paid", "Top Therapeutic Class Utilization Ranked by Volume",
    "Utilization And Cost by Provider Type", "Utilization by Age Group", "Utilization by Diagnosis"].
    For ticket-related queries, use this tool if the query contains any of: ['submit ticket', 'ticket', 'ticket submit'].
    For feedback-related queries, use this tool if the query contains any of: ['submit feedback', 'feedback', 'feedback submit'].
    Do not use your own judgement to call this tool; only call it when the user's query contains one of the specified keywords.
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """
    try:
        uuid = state.get("user_info", {}).get("uuid")
        check = keywords_cer_ticket_feedback(query)
        if check == "cer":
            cer_link = os.getenv("CER_LINK")
            if not cer_link:
                return "Sorry, I couldn't find the CER link. Please check the configuration."
            # log user activity here
            store_user_tracking_info(uuid, "Moved to CER", '', state.get("user_info", {}))
            return {
                "answer": "Redirecting you to the CER link.",
                "custom_suggestions": [{
                    "cer_link": cer_link,
                    "type": "cer_link",
                    }]
            }
        elif check == "ticket":
            gco_report = await get_redis(state, uuid)
            body= {
                    "user_id": uuid,
                    "user_type": "external",
                    "user_access_role": "Key Accounts & National Accounts",
                    "user_ext_type": "Broker",
                    "action_type": "Submit Ticket",
                    "incident_number": "",
                    "group_id": "",
                    "group_name": "",
                    "user_name": "",
                    "user_email": "",
                    "file_name": "",
                    "report_type": "",
                    "published_at":"",
                    "platform_id": "",
                    "funding_type": "",
                    "session_id": ""  
                }
            if not gco_report:
                groupData =[{'value': 'Other', 'label': 'Other'}]
                ReportData = {
                    "Other": [{"value": "Other", "label": "Other"}]
                }
            else:    
                group_report_dict = {}

                for group in gco_report:
                    group_name = group.get("group_name")
                    reports = group.get("reports", [])
                    report_types = [report.get("report_type") for report in reports]
                    group_report_dict[group_name] = report_types
                group_report_dict["Other"] = ["Other"]
                groupData = [{"value": group, "label": group} for group in group_report_dict.keys()]
                ReportData = {
                    group: [{"value": report, "label": report} for report in reports]
                    for group, reports in group_report_dict.items()
                }
            return {
                    "answer": "",
                    "custom_suggestions": [{
                        "groupData": groupData,
                        "ReportData": ReportData,
                        "type": "ticket",
                        "body": body
                    }]
                    
                }
        elif check == "feedback":
            body =  {
                    "user_id": uuid,
                    "user_type": "external",
                    "user_access_role": "Key Accounts & National Accounts",
                    "user_ext_type": "Broker",
                    "action_type": "Submit Feedback",
                    "incident_number": "",
                    "group_id": "",
                    "group_name": "",
                    "user_name": "",
                    "user_email": "",
                    "file_name": "",
                    "report_type": "",
                    "published_at":"",
                    "platform_id": "",
                    "funding_type": "",
                    "session_id": ""  
                }
            return {
                    "answer": "",
                    "custom_suggestions": [{
                        "type": "feedback",
                        "body": body
                    }]
                    
                }

        else:
            return "Sorry, I couldn't understand your request. Please provide more details or use keywords like words related to CER report, 'submit ticket', or 'submit feedback'."
    
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "get_CER_link")
        traceback.format_exc()


@tool
async def get_top_downloaded_reports(state: Annotated[dict, InjectedState], group: str = None, report_type: str = None, num_reports: int = 5):
    """
    This tool provides the capability to get the top 5 downloaded reports for a given group and report type.
    It fetches reports sorted by download count in descending order. It is used when the user asks for the top 5 downloaded reports.
    This tool can be called at the very beginning of the conversation. The user may or may not provide details such as group name, report type, or number of top downloaded reports to fetch. Always call this tool if the user asks for top most, highest, maximum or large number of downloaded reports in terms of count of downloads, regardless of how much information the user provides.
    For example user query is "show me top 5 downloaded reports for all group", then call this tool. The report with maximum download should be visible at the top followed by the next highest downloaded report, and so on.
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """
    try:
        uuid = "Demo User-570425"
        gco_report = await get_mdr(uuid)

        if not gco_report:
            return "Sorry, I couldn't find any reports. Please check the data and try again."
        report_details = get_downloaded_report_details(gco_report, group=group, report_type=report_type, num_reports=num_reports)
        if not report_details:
            return "Sorry, I couldn't find any reports for the specified filters."

        custom_suggestions_list = []
        for report in report_details:
            body = {
                "user_type": "external",
                "user_access_role": "Key Accounts & National Accounts",
                "user_ext_type": "Broker",
                "action_type": "download",
                "incident_number": "",
                "group_id": report.get("group_id", ""),
                "group_name": report.get("group_name", ""),
                "user_name": report.get("user_name", ""),
                "user_email": report.get("user_email", ""),
                "file_name": report.get("file_name", ""),
                "report_type": report.get("report_type", ""),
                "published_at": report.get("published_at", ""),
                "platform_id": report.get("platform_id", ""),
                "funding_type": report.get("funding_type", ""),
                "session_id": ""  # is needed for tracking purposes?
            }

            custom_suggestions = {
                "file_url": report.get("report_link", ""),
                "file_date": report.get("published_at", ""),
                "report_type": report.get("report_type", ""),
                "type": "download",
                "group": report.get("group_name", ""),
                "download_info": body,
                "extra_info": report.get("download_count", "")
            }
            custom_suggestions_list.append(custom_suggestions)

        return {
            "answer": f"Here are the top {num_reports} downloaded reports. Which one would you like to download?",
            "custom_suggestions": custom_suggestions_list
        }
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "get_top_downloaded_reports")
        traceback.format_exc()

@tool
async def get_recent_downloaded_reports(state: Annotated[dict, InjectedState], group: str = None, report_type: str = None, num_reports: int = None):
    """
    This tool provides the capability to get the recent downloaded reports for a given group and report type.
    It fetches reports sorted by timestamp of download in descending order. It is used when the user asks for all the recent downloaded reports.
    This tool can be called at the very beginning of the conversation. The user may or may not provide details such as group name, report type, or number of reports to fetch. Always call this tool if the user asks for new, latest, or recently downloaded reports, regardless of how much information the user provides.
    For example user query is "show me recent downloaded reports for all group", then call this tool. The report which is downloaded few days ago should be visible at the top followed by the next recent downloaded report, and so on.
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """
    try:
        uuid = "Demo User-570425"
        gco_report = await get_rdr(uuid)
        if not gco_report:
            return "Sorry, I couldn't find any reports. Please check the data and try again."

        report_details = get_recent_report_details(gco_report, group=group, report_type=report_type, num_reports=num_reports)
        if not report_details:
            return "Sorry, I couldn't find any reports for the specified filters."

        custom_suggestions_list = []
        for report in report_details:
            body = {
                "user_type": "external",
                "user_access_role": "Key Accounts & National Accounts",
                "user_ext_type": "Broker",
                "action_type": "download",
                "incident_number": "",
                "group_id": report.get("group_id", ""),
                "group_name": report.get("group_name", ""),
                "user_name": report.get("user_name", ""),
                "user_email": report.get("user_email", ""),
                "file_name": report.get("file_name", ""),
                "report_type": report.get("report_type", ""),
                "published_at": report.get("published_at", ""),
                "platform_id": report.get("platform_id", ""),
                "funding_type": report.get("funding_type", ""),
                "session_id": ""  # is needed for tracking purposes?
            }

            custom_suggestions = {
                "file_url": report.get("report_link", ""),
                "file_date": report.get("published_at", ""),
                "report_type": report.get("report_type", ""),
                "type": "download",
                "group": report.get("group_name", ""),
                "download_info": body,
                "extra_info": report.get("updated_timestamp", "")
            }
            custom_suggestions_list.append(custom_suggestions)

        return {
            "answer": f"Here are the recent reports. Which one would you like to download?",
            "custom_suggestions": custom_suggestions_list
        }
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "get_recent_downloaded_reports")
        traceback.format_exc()

async def get_not_downloaded_reports(state: Annotated[dict, InjectedState], group: str = None, report_type: str = None):
    """
    This tool provides the capability to get reports not yet downloaded for a given group and report type.It is used when the user asks for all the reports which is not yet downloaded.
    This tool can be called at the very beginning of the conversation. The user may or may not provide details such as group name, report type, or number of reports not downloaded. Always call this tool if the user asks for reports which is not downloaded, regardless of how much information the user provides.
    For example user query is "show me all the reports which I've not yet downloaded for all group", then call this tool.
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """
    try:
        uuid = state.get("user_info", {}).get("uuid")
        gco_report = await get_redis(state, uuid)
        download_data = await get_ndr("Demo User-570425")
        limited_reports = get_not_downloaded_report_details(gco_report, download_data, group=group, report_type=report_type)
        if not limited_reports:
            return "Sorry, I couldn't find any reports for the specified filters."

        custom_suggestions_list = []
        for report in limited_reports:
            body = {
                "user_type": "external",
                "user_access_role": "Key Accounts & National Accounts",
                "user_ext_type": "Broker",
                "action_type": "download",
                "incident_number": "",
                "group_id": report.get("group_id", ""),
                "group_name": report.get("group_name", ""),
                "user_name": report.get("user_name", ""),
                "user_email": report.get("user_email", ""),
                "file_name": report.get("file_name", ""),
                "report_type": report.get("report_type", ""),
                "published_at": report.get("published_at", ""),
                "platform_id": report.get("platform_id", ""),
                "funding_type": report.get("funding_type", ""),
                "session_id": ""  # is needed for tracking purposes?
            }

            custom_suggestions = {
                "file_url": report.get("report_link", ""),
                "file_date": report.get("published_at", ""),
                "report_type": report.get("report_type", ""),
                "type": "download",
                "group": report.get("group_name", ""),
                "download_info": body
            }
            custom_suggestions_list.append(custom_suggestions)

        return {
            "answer": f"Here are the reports not yet downloaded. Which one would you like to download?",
            "custom_suggestions": custom_suggestions_list
        }
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "get_not_downloaded_reports")
        traceback.format_exc()
