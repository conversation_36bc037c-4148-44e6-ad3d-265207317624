from client_plugins.bnemobile.comm_center.main import comm_center_tool_node
from client_plugins.bnemobile.comm_center.main import comm_center_runnable
from client_plugins.bnemobile.comm_center.router import route_comm_center_assistant
from graph.nodes import Assistant
from langgraph.pregel import RetryPolicy
from graph.nodes import pop_dialog_state, escalation_fallback

from langgraph.graph import START
from graph.state import State, get_postgres_checkpointer
from utils.subgraph_builder import BaseSubgraphBuilder
from utils.subgraph_registry import SubgraphRegistry

comm_center_compiled_graph = None

async def init_comm_center_graph():
    """
    Initialize the comm center graph using the BaseSubgraphBuilder pattern
    and register it with SubgraphRegistry.
    """
    global comm_center_compiled_graph
    if comm_center_compiled_graph is None:
        # Create a new builder with fluent interface
        builder = BaseSubgraphBuilder(State)
        
        # Add nodes
        builder.add_node("comm_center_assistant", Assistant(comm_center_runnable), retry=RetryPolicy(max_attempts=2))
        builder.add_node("call_comm_center_tool", comm_center_tool_node)
        builder.add_node("leave_skill", pop_dialog_state)
        builder.add_node("escalation_fallback", escalation_fallback)
        
        # Add edges
        builder.add_edge("leave_skill", "comm_center_assistant")
        builder.add_edge("escalation_fallback", "comm_center_assistant")
        builder.add_edge(START, "comm_center_assistant")
        
        # Add conditional edges
        builder.add_conditional_edges("comm_center_assistant", route_comm_center_assistant)
        
        # Compile with async checkpointer
        comm_center_compiled_graph = builder.compile(checkpointer=await get_postgres_checkpointer())
        
        # Register with SubgraphRegistry for future access
        SubgraphRegistry.register("comm_center", comm_center_compiled_graph)