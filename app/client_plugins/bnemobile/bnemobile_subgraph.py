from langgraph.graph import START
from graph.state import State, get_postgres_checkpointer
from graph.nodes import Assistant
from langgraph.pregel import RetryPolicy
from graph.chains import fetch_primary_assistant_runnable
from langgraph.prebuilt import ToolNode
from utils.helpers.constants import TOOL_CALL_ERROR_MESSAGE
from graph.nodes import restrict_parallel_agents_run
from client_plugins.bnemobile.workflow_router import route_to_workflow, route_bnemobile_assistant
from utils.subgraph_utils import create_entry_message
from utils.helpers.constants import OEC_BNEMOBILE_CLIENT_ID
from utils.subgraph_builder import BaseSubgraphBuilder
from utils.subgraph_registry import SubgraphRegistry

bnemobile_compiled_graph = None

async def init_bnemobile_graph():
    """
    Initialize the BNE Mobile graph using the BaseSubgraphBuilder pattern
    and register it with SubgraphRegistry.
    """
    global bnemobile_compiled_graph
    if bnemobile_compiled_graph is None:
        # Get the compiled subgraphs from the registry
        comm_centre_search_compiled_graph = SubgraphRegistry.get("comm_center")
        sbc_retrieval_agent_enrollment_compiled_graph = SubgraphRegistry.get("sbc_retrieval_agent")

        # Create a new builder with fluent interface
        builder = BaseSubgraphBuilder(State)
        
        # Get tools and assistant
        primary_runnable, primary_tools = fetch_primary_assistant_runnable(OEC_BNEMOBILE_CLIENT_ID)
        primary_assistant_tool_node = ToolNode(primary_tools, handle_tool_errors=TOOL_CALL_ERROR_MESSAGE)
        
        # Add nodes
        builder.add_node("primary", Assistant(primary_runnable), retry=RetryPolicy(max_attempts=2))
        builder.add_node("enter_comm_centre_assistant", create_entry_message('Comm Centre search assistant', 'comm_centre_subgraph'))
        builder.add_node("comm_centre_subgraph", comm_centre_search_compiled_graph)
        builder.add_node("enter_sbc_retrieval_agent_assistant", create_entry_message('SBC Retrieval assistant', 'sbc_retrieval_agent_subgraph'))
        builder.add_node("sbc_retrieval_agent_subgraph", sbc_retrieval_agent_enrollment_compiled_graph)
        builder.add_node("invalid_tool", primary_assistant_tool_node)
        builder.add_node("restrict_parallel_agents_run", restrict_parallel_agents_run)
        
        # Add edges
        builder.add_edge("enter_comm_centre_assistant", "comm_centre_subgraph")
        builder.add_edge("enter_sbc_retrieval_agent_assistant", "sbc_retrieval_agent_subgraph")
        builder.add_edge("invalid_tool", "primary")
        builder.add_edge("restrict_parallel_agents_run", "primary")
        
        # Set conditional entry point
        builder.add_conditional_edges(START, route_to_workflow)
        
        # Add conditional edges for routing
        builder.add_conditional_edges("primary", route_bnemobile_assistant)
        
        # Compile with async checkpointer
        bnemobile_compiled_graph = builder.compile(checkpointer=await get_postgres_checkpointer())
        
        # Register with SubgraphRegistry for future access
        SubgraphRegistry.register("bnemobile", bnemobile_compiled_graph)