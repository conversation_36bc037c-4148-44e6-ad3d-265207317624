from client_plugins.bnemobile.sbc_retrieval_agent.main import sbc_retrieval_agent_tool_node
from client_plugins.bnemobile.sbc_retrieval_agent.main import sbc_retrieval_agent_runnable
from client_plugins.bnemobile.sbc_retrieval_agent.router import route_sbc_retrieval_agent_assistant
from graph.nodes import Assistant
from langgraph.pregel import RetryPolicy
from graph.nodes import pop_dialog_state, escalation_fallback

from langgraph.graph import START
from graph.state import State, get_postgres_checkpointer
from utils.subgraph_builder import BaseSubgraphBuilder
from utils.subgraph_registry import SubgraphRegistry

sbc_retrieval_agent_compiled_graph = None

async def init_sbc_retrieval_agent_graph():
    """
    Initialize the sbc retrieval agent graph using the BaseSubgraphBuilder pattern
    and register it with SubgraphRegistry.
    """
    global sbc_retrieval_agent_compiled_graph
    if sbc_retrieval_agent_compiled_graph is None:
        # Create a new builder with fluent interface
        builder = BaseSubgraphBuilder(State)
        
        # Add nodes
        builder.add_node("sbc_retrieval_agent_assistant", Assistant(sbc_retrieval_agent_runnable), retry=RetryPolicy(max_attempts=2))
        builder.add_node("call_sbc_retrieval_agent_tool", sbc_retrieval_agent_tool_node)
        builder.add_node("leave_skill", pop_dialog_state)
        builder.add_node("escalation_fallback", escalation_fallback)
        
        # Add edges
        builder.add_edge("leave_skill", "sbc_retrieval_agent_assistant")
        builder.add_edge("escalation_fallback", "sbc_retrieval_agent_assistant")
        builder.add_edge(START, "sbc_retrieval_agent_assistant")
        
        # Add conditional edges
        builder.add_conditional_edges("sbc_retrieval_agent_assistant", route_sbc_retrieval_agent_assistant)
        
        # Compile with async checkpointer
        sbc_retrieval_agent_compiled_graph = builder.compile(checkpointer=await get_postgres_checkpointer())
        
        # Register with SubgraphRegistry for future access
        SubgraphRegistry.register("sbc_retrieval_agent", sbc_retrieval_agent_compiled_graph)