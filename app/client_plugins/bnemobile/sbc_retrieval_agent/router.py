
from graph.state import State
from langgraph.prebuilt import tools_condition
from langgraph.graph import END
from tools.common_tools import CompleteOrEscalate

# Router: This is to be used for merging the capability, it is recommended not to modify it

def route_sbc_retrieval_agent_assistant(
    state: State,
):
    route = tools_condition(state)
    if route == END:
        return END
    if route == "tools":
        tool_calls = state["messages"][-1].tool_calls
        if tool_calls[0]["name"] == CompleteOrEscalate.__name__:
            return "leave_skill"
        return "call_sbc_retrieval_agent_tool"
    return "sbc_retrieval_agent_assistant"
    