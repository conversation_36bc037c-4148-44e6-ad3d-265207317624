
from langchain_core.tools import tool
from langgraph.prebuilt.tool_node import InjectedState
import traceback
from typing import Annotated
import json
from utils.tool_utils import log_tool_error
    
@tool("get_sbc", return_direct=True)
async def get_sbc(state: Annotated[dict, InjectedState], planId:str, planName:str) -> str:
    """
    This tool is to retrieve the SBC for a user using the corresponding plan information. 
    If the user is requesting an sbc for a group or member, 
        this tool can only be called once a user has confirmed the plan for the group or member.
    If the user is requesting an sbc for a plan directly, this tool can be called directly with the plan ID.
    Plan Ids are different from group IDs and member IDs.
    
    Args:
        planId: The ID of the plan the user wants to retrieve the SBC for.
        planName: The name of the plan the user wants to retrieve the SBC for.
    """
    try:
        # TODO: call SBC API, return base64 directly 
        plan_info = {
            "planId": planId,
            "planName": planName
        }
        return json.dumps(plan_info)
    except Exception:
        await log_tool_error(state, traceback.print_exc(), "get_sbc")
        raise Exception (traceback.format_exc())
