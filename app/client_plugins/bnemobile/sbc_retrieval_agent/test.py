import sys
import os
import asyncio
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..', '..', '..')))
from dotenv import load_dotenv
load_dotenv()
from utils.tool_utils import _print_event
import uuid
from client_plugins.bnemobile.sbc_retrieval_agent.sbc_retrieval_agent_subgraph import init_sbc_retrieval_agent_graph
from config.postgres import init_auto_commit_pool,init_connection_pool ,close_connection_pools
from graph.state import get_postgres_checkpointer
from utils.subgraph_registry import SubgraphRegistry
from utils.agent_utils import clear_and_summarize_messages

async def start_sbc_retrieval_agent_graph():
    if SubgraphRegistry.get("sbc_retrieval_agent") is None:
        await init_connection_pool()
        await init_auto_commit_pool()
        await get_postgres_checkpointer()
        await init_sbc_retrieval_agent_graph()
    sbc_retrieval_agent_graph = SubgraphRegistry.get("sbc_retrieval_agent")
    return sbc_retrieval_agent_graph

_printed = set()
async def run_agent(queryString, session_id, graph, user_name=None, uuid=None, additional_arg=None):
    additional_arg = additional_arg or {}
    thread_id = session_id
    config = {
        "configurable": {
            "thread_id": thread_id,
        }
    }
    user_info = {"client_id": "Internal", "uuid": uuid, "session_id": session_id, "user_name": user_name}

    events = graph.astream(
        {
            "messages": ("user", queryString),
            "user_info": user_info,
            "is_multiagent": False,
            "is_auth_performed": True,
            "additional_arg": additional_arg 
        },
        config,
        stream_mode="values"
    )
    async for event in events:
        response = _print_event(event, _printed)
    
    await clear_and_summarize_messages(graph, config)
    return response

async def main():
    graph = await start_sbc_retrieval_agent_graph()
    thread_id = str(uuid.uuid4())
    try:
        while True:
            input_text = input("Enter your question or press 'q' to exit: ")
            if input_text.lower() in ["quit", "exit", "q"]:
                print("Goodbye! Have a nice day!")
                print("Exiting...")
                break
            response = await run_agent(input_text, thread_id, graph)
    finally:
        await close_connection_pools()

if __name__ == "__main__":
    asyncio.run(main())
