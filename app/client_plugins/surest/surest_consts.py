import os
env = os.getenv("ENV")

SUREST_TRAINING_MATERIAL_TABLE_NAME = "surest_training_material"
SUREST_TRAINING_MATERIAL_VIDEO_TABLE_NAME = "surest_training_material_video"

SUREST_PLAN_COMPARISION_DATA = {
    "benefit": 
        {
            "color": "$interactive3",
            "rows": [
                {
                    "title": "Proven savings",
                    "description": "members had 50% average lower member out-of-pocket spend&sup1; and employers saved up to 15%&sup2;",
                },
                {
                    "title": "Price transparency",
                    "description": "see actual copays, not estimates, before an appointment with lower copays assigned to providers evaluated as high value"
                },
                {
                    "title": "Reduced barriers",
                    "description": "no deductibles or coinsurance thresholds to meet before benefits start working"
                },
                {
                    "title": "Broad network",
                    "description": "Access to the national UnitedHealthcare provider network"
                },
                {
                    "title": "Intuitive digital experience",
                    "description": "members can access the Surest app or website to shop for ca"
                }
                
            ],
            "sups": [
                {
                "description":  "&sup1;Members who migrated from a non-Surest plan into a Surest plan in 2022, compared to those who stayed with a non-Surest plan."
                },
                {
                "description": "&sup2;Surest actuarial results through 2024."
                }
            ]
        },
    "planComparisions": {
        "uhc": {
            "planName": "UNITEDHEALTHCARE CHOICE",
            "planCode": "E1000i100LX21B",
            "monthlyCost": "$2,432.44",
            "deductible": "$1000",
            "oopMax": "$3500",
            "hospCoins": "Ded+Coin",
            "pcpCopay": "$25",
            "specialistCopay": "$75"
        },
        "surest": {
            "planName": "UNITEDHEALTHCARE CHOICE",
            "planCode": "SurestCA30002025RXALT1",
            "monthlyCost": "$2,493.98",
            "deductible": "No Deductible",
            "oopMax": "$3700",
            "hospCoins": "$75 to $2000",
            "pcpCopay": "$5 to $40",
            "specialistCopay": "$5 to $40",
        },
        "highlights": {
            "uhc": [
                "pcpCopay"
            ],
            "surest": [
                "deductible",
                "oopMax",
                "specialistCopay"
            ]
        }
    }
}