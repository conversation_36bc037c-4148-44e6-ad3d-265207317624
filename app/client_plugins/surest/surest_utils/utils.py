from config.openAI import embeddings
from config.postgres import get_pg_connection, return_pg_connection
import pdfplumber
from langchain.text_splitter import RecursiveCharacterTextSplitter
from client_plugins.surest.surest_consts import SUREST_TRAINING_MATERIAL_TABLE_NAME, SUREST_TRAINING_MATERIAL_VIDEO_TABLE_NAME
import os
import json
import io
from datetime import datetime

async def create_or_update_surest_training_video(video_name, video_url, video_description):

    pg_connection = await get_pg_connection()
    cursor = pg_connection.cursor()
    
    # Create table if it doesn't exist
    create_table_query = f"""
    CREATE TABLE IF NOT EXISTS {SUREST_TRAINING_MATERIAL_VIDEO_TABLE_NAME} (
        id SERIAL PRIMARY KEY,
        file_name TEXT,
        file_url TEXT,
        page_content TEXT,
        embedding vector(1536), 
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """
    await cursor.execute(create_table_query)
    await pg_connection.commit()
    
    # Generate embedding for the document content
    embedding = await embeddings.aembed_query(video_description)

    # Check if video document already exists    
    await delete_surest_training_material(table_name=SUREST_TRAINING_MATERIAL_VIDEO_TABLE_NAME, file_name=video_name)
    
    # Insert new video document
    insert_query = f"""
    INSERT INTO {SUREST_TRAINING_MATERIAL_VIDEO_TABLE_NAME} (file_name, file_url, page_content, embedding, created_at)
    VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
    """
    # cursor.execute(insert_query, (video_document.file_name, video_document.file_url, video_document.page_content, embedding))
    await cursor.execute(insert_query, (video_name, video_url, video_description, embedding))

    await pg_connection.commit()
    await cursor.close()
    await return_pg_connection(pg_connection)

async def create_or_update_surest_training_material(file, file_name, file_url):
    # Get a connection from the pool
    pg_connection = await get_pg_connection()
    cursor = pg_connection.cursor()

    # Create table if it doesn't exist 
    create_table_query = f"""
    CREATE TABLE IF NOT EXISTS {SUREST_TRAINING_MATERIAL_TABLE_NAME} (
        id SERIAL PRIMARY KEY,
        file_name TEXT,
        file_url TEXT,
        page_content TEXT,
        embedding vector(1536), 
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """
    await cursor.execute(create_table_query)
    await pg_connection.commit()

    # Read the PDF file
    pdf_buffer = io.BytesIO(await file.read())
    pdf_text = ""
    with pdfplumber.open(pdf_buffer) as pdf:
        for page in pdf.pages:
            pdf_text += page.extract_text()

    # Split the docs into chunks
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000,
        chunk_overlap=200,
        separators=["\n\n","\n", "", ""]
    )
    # Assuming text_splitter is an instance of a text splitting class
    chunks = text_splitter.split_text(pdf_text)
    
    # delete the existing docs for file_name
    await delete_surest_training_material(table_name=SUREST_TRAINING_MATERIAL_TABLE_NAME, file_name=file_name)

    # Prepare the data for batch insertion
    data_to_insert = []
    for chunk in chunks:
        embedding = await embeddings.aembed_query(chunk)
        data_to_insert.append((file_name, file_url, chunk, embedding))

    # Batch insert the data
    insert_query = f"""
    INSERT INTO {SUREST_TRAINING_MATERIAL_TABLE_NAME} (file_name, file_url, page_content, embedding, created_at)
    VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
    """
    await cursor.executemany(insert_query, data_to_insert)
    await pg_connection.commit()
    await cursor.close()
    await return_pg_connection(pg_connection)

async def delete_surest_training_material(table_name, file_name):
    pg_connection = await get_pg_connection()
    cursor = pg_connection.cursor()
    # Pass file_name as a tuple
    fetch_query = """SELECT id FROM {} WHERE file_name = %s""".format(table_name)
    delete_query = """DELETE FROM {} WHERE id = ANY(%s)""".format(table_name)

    await cursor.execute(fetch_query, (file_name,))
    await pg_connection.commit()
    existing_records = await cursor.fetchall()
    
    if existing_records:
        # Collect IDs of the documents to delete
        ids_to_delete = [record[0] for record in existing_records]

        # Delete the existing documents
        await cursor.execute(delete_query, (ids_to_delete,))
        await pg_connection.commit()

    await cursor.close()
    await return_pg_connection(pg_connection)

async def get_all_surest_training_materials(table_name):
    pg_connection = await get_pg_connection()
    cursor = pg_connection.cursor()

    fetch_query = """ SELECT file_name, file_url, MIN(created_at) as created_at FROM {} GROUP BY file_name, file_url """.format(table_name)

    await cursor.execute(fetch_query)
    await pg_connection.commit()
    records = await cursor.fetchall()
    
    # Convert datetime objects to strings
    formatted_records = []
    for record in records:
        file_name, file_url, created_at = record
        # created_at = record[2]
        if isinstance(created_at, datetime):
            created_at = created_at.strftime("%Y-%m-%d %H:%M:%S")  # Convert datetime to string

        formatted_record = {
            "file_name": file_name,
            "file_url": file_url,
            "created_at": created_at # Convert datetime to string
        }
        formatted_records.append(formatted_record)

    await cursor.close()
    await return_pg_connection(pg_connection)
    return formatted_records