from client_plugins.surest.surest.tool import surest_training_material_qa, surest_training_video_qa, surest_plan_comparision
from services.get_prompts_service import master_prompt_document, get_prompt_by_assistant_name
from graph.state import State
from graph.nodes import Assistant
from langchain_core.prompts import ChatPromptTemplate
# from config.openAI import model
from config.llamaAI import model
from langgraph.graph import StateGraph, START
from langgraph.prebuilt import tools_condition
from tools.common_tools import CompleteOrEscalate
from langgraph.prebuilt import ToolNode
from utils.helpers.constants import TOOL_CALL_ERROR_MESSAGE


surest_assistant_prompt_document = get_prompt_by_assistant_name("surest_assistant")

if not surest_assistant_prompt_document:
    raise ValueError("Surest Assistant Prompt Not Found")

master_prompt = master_prompt_document.get("masterPrompt")
assistant_prompt = surest_assistant_prompt_document.get("assistantPrompt")
combined_prompt = (master_prompt + "\n" + assistant_prompt)

surest_system_prompt = ChatPromptTemplate.from_messages([
    ("system", combined_prompt),
    ("placeholder", "{messages}")                                       
]).partial()

surest_tools = [surest_training_material_qa, surest_training_video_qa, surest_plan_comparision] + [CompleteOrEscalate]
surest_tool_node = ToolNode(surest_tools, handle_tool_errors = TOOL_CALL_ERROR_MESSAGE)
surest_runnable = surest_system_prompt | model.bind_tools(surest_tools)