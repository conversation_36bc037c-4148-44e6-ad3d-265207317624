from client_plugins.surest.surest.main import surest_tool_node
from client_plugins.surest.surest.main import surest_runnable
from client_plugins.surest.surest.router import route_surest_assistant
from graph.nodes import Assistant
from langgraph.pregel import RetryPolicy
from graph.nodes import pop_dialog_state, escalation_fallback

from langgraph.graph import START
from graph.state import State, get_postgres_checkpointer
from utils.subgraph_builder import BaseSubgraphBuilder
from utils.subgraph_registry import SubgraphRegistry

surest_compiled_graph = None

async def init_surest_graph():
    """
    Initialize the surest graph using the BaseSubgraphBuilder pattern
    and register it with SubgraphRegistry.
    """
    global surest_compiled_graph
    if surest_compiled_graph is None:
        # Create a new builder with fluent interface
        builder = BaseSubgraphBuilder(State)
        
        # Add nodes
        builder.add_node("surest_assistant", Assistant(surest_runnable), retry=RetryPolicy(max_attempts=2))
        builder.add_node("call_surest_tool", surest_tool_node)
        builder.add_node("leave_skill", pop_dialog_state)
        builder.add_node("escalation_fallback", escalation_fallback)
        
        # Add edges
        builder.add_edge("leave_skill", "surest_assistant")
        builder.add_edge("escalation_fallback", "surest_assistant")
        builder.add_edge(START, "surest_assistant")
        
        # Add conditional edges
        builder.add_conditional_edges("surest_assistant", route_surest_assistant)
        
        # Compile with async checkpointer
        surest_compiled_graph = builder.compile(checkpointer=await get_postgres_checkpointer())
        
        # Register with SubgraphRegistry for future access
        SubgraphRegistry.register("surest", surest_compiled_graph)