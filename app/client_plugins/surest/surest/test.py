import sys
import os
import asyncio
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..', '..', '..')))
from dotenv import load_dotenv
load_dotenv()
from utils.tool_utils import _print_event
import uuid
# from client_plugins.surest.surest.main import surest_builder
from client_plugins.surest.surest.surest_subgraph import init_surest_graph
from config.postgres import init_connection_pool, init_auto_commit_pool, close_connection_pools
from graph.state import get_postgres_checkpointer
from utils.subgraph_registry import SubgraphRegistry

async def start_surest_graph():
    if SubgraphRegistry.get("surest") is None:
        await init_connection_pool()
        await init_auto_commit_pool()
        await get_postgres_checkpointer()
        await init_surest_graph()
    surest_graph = SubgraphRegistry.get("surest")
    return surest_graph

_printed = set()
async def run_agent(queryString, uuid, graph, additional_arg=None):
    additional_arg = additional_arg or {}
    thread_id = uuid
    config = {
        "configurable": {
            "thread_id": thread_id,
        }
    }
    user_info = {"client_id": "Internal", "uuid": uuid}
    async for event in graph.astream(
        {
            "messages": ("user", queryString),
            "user_info": user_info,
            "is_multiagent": False,
            "is_auth_performed": True,
            "additional_arg": additional_arg, 
        },
        config,
        stream_mode="values"
    ):
        response = _print_event(event, _printed)

    return response

async def main():
    # Set up the graph once at the beginning
    graph = await start_surest_graph()
    thread_id = str(uuid.uuid4())
    try:
        while True:
            input_text = input("Enter your question or press 'q' to exit: ")
            if input_text.lower() in ["quit", "exit", "q"]:
                print("Goodbye! Have a nice day!")
                print("Exiting...")
                break
            response = await run_agent(input_text, thread_id, graph)
    finally:
        await close_connection_pools()

if __name__ == "__main__":
    asyncio.run(main())