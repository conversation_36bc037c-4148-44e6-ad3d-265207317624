from utils.general_utils import extract_values
from tools.primary_assistant_tools.router_tools import ToDocumentSearchAssistant, ToPortalFormEnrollmentAssistant
from graph.state import State
from typing import Literal
from services.event_logger_service import logger
from utils.general_utils import validate_tool_call
from langgraph.prebuilt import tools_condition
from langgraph.graph import END
from tools.primary_assistant_tools.router_tools import router_tools

async def route_to_workflow(
    state: State,
) -> Literal[
    "primary",
    "portal_form_enrollment_subgraph",
    "document_search_subgraph"
]:
    """If we are in a delegated state, route directly to the appropriate assistant."""
    dialog_state = state.get("dialog_state")
    dialog_state = extract_values(dialog_state)
    #INFO: Log agent traversal
    uuid = state.get("user_info")["uuid"]
    msid = uuid.split('-')[0]
    await logger.info(uuid, msid, state.get("user_info")["client_id"], "router", "route_to_workflow", "State", dialog_state[-1] if dialog_state else "primary", None, None)
    if not dialog_state:
        return "primary"
    
    return dialog_state[-1]

async def route_bne_portal_assistant(
    state: State,
):  
    available_tools = [ToDocumentSearchAssistant, ToPortalFormEnrollmentAssistant]
    route = tools_condition(state)
    if route == END:
        return END
    if route == "tools" and validate_tool_call(state["messages"][-1].tool_calls, available_tools):
        tool_calls = state["messages"][-1].tool_calls
        if len(tool_calls) > 1:
            return "restrict_parallel_agents_run"
        
        if tool_calls[0]["name"] in router_tools:
            uuid = state.get("user_info")["uuid"]
            msid = uuid.split('-')[0]
            await logger.info(uuid, msid, state.get("user_info")["client_id"], "router", "route_primary_assistant", "State", tool_calls[0]["name"], None, None)
        if tool_calls[0]["name"] == ToDocumentSearchAssistant.__name__ :
            return "enter_document_search_assistant"
        elif tool_calls[0]["name"] == ToPortalFormEnrollmentAssistant.__name__ :
            return "enter_portal_form_enrollment_assistant"
    elif route == "tools":
        return "invalid_tool"
    return "primary"