from langgraph.graph import START
from graph.state import State, get_postgres_checkpointer
from graph.nodes import Assistant
from langgraph.pregel import RetryPolicy
from graph.chains import fetch_primary_assistant_runnable
from langgraph.prebuilt import ToolNode
from utils.helpers.constants import TOOL_CALL_ERROR_MESSAGE
from graph.nodes import restrict_parallel_agents_run
from client_plugins.bneportal.workflow_router import route_to_workflow, route_bne_portal_assistant
from utils.subgraph_utils import create_entry_message
from utils.helpers.constants import OEC_BNEPORTAL_CLIENT_ID
from utils.subgraph_builder import BaseSubgraphBuilder
from utils.subgraph_registry import SubgraphRegistry

bneportal_compiled_graph = None

async def init_bneportal_graph():
    """
    Initialize the BNE Portal graph using the BaseSubgraphBuilder pattern
    and register it with SubgraphRegistry.
    """
    global bneportal_compiled_graph
    if bneportal_compiled_graph is None:
        # Get the compiled subgraphs from the registry
        nonauth_documents_search_compiled_graph = SubgraphRegistry.get("nonauth_documents_search")
        portal_form_enrollment_compiled_graph = SubgraphRegistry.get("portal_form_enrollment")

        # Create a new builder with fluent interface
        builder = BaseSubgraphBuilder(State)
        
        # Get tools and assistant
        primary_runnable, primary_tools = fetch_primary_assistant_runnable(OEC_BNEPORTAL_CLIENT_ID)
        primary_assistant_tool_node = ToolNode(primary_tools, handle_tool_errors=TOOL_CALL_ERROR_MESSAGE)
        
        # Add nodes
        builder.add_node("primary", Assistant(primary_runnable), retry=RetryPolicy(max_attempts=2))
        builder.add_node("enter_document_search_assistant", create_entry_message('document search assistant', 'document_search_subgraph'))
        builder.add_node("document_search_subgraph", nonauth_documents_search_compiled_graph)
        builder.add_node("enter_portal_form_enrollment_assistant", create_entry_message('portal form enrollment assistant', 'portal_form_enrollment_subgraph'))
        builder.add_node("portal_form_enrollment_subgraph", portal_form_enrollment_compiled_graph)
        builder.add_node("invalid_tool", primary_assistant_tool_node)
        builder.add_node("restrict_parallel_agents_run", restrict_parallel_agents_run)
        
        # Add edges
        builder.add_edge("enter_document_search_assistant", "document_search_subgraph")
        builder.add_edge("enter_portal_form_enrollment_assistant", "portal_form_enrollment_subgraph")
        builder.add_edge("invalid_tool", "primary")
        builder.add_edge("restrict_parallel_agents_run", "primary")
        
        # Set conditional entry point
        builder.add_conditional_edges(START, route_to_workflow)
        
        # Add conditional edges for routing
        builder.add_conditional_edges("primary", route_bne_portal_assistant)
        
        # Compile with async checkpointer
        bneportal_compiled_graph = builder.compile(checkpointer=await get_postgres_checkpointer())
        
        # Register with SubgraphRegistry for future access
        SubgraphRegistry.register("bneportal", bneportal_compiled_graph)