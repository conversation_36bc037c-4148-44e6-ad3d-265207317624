import sys
import os
import asyncio
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..', '..', '..')))
from dotenv import load_dotenv
load_dotenv()
from utils.tool_utils import _print_event
import uuid
# from client_plugins.bneportal.portal_form_enrollment.main import portal_form_enrollment_builder
from client_plugins.bneportal.portal_form_enrollment.portal_form_enrollment_subgraph import init_portal_form_enrollment_graph
from config.postgres import init_connection_pool,init_auto_commit_pool,close_connection_pools
from graph.state import get_postgres_checkpointer
from utils.subgraph_registry import SubgraphRegistry

async def start_portal_form_enrollment_graph():
    if SubgraphRegistry.get("portal_form_enrollment") is None:
        await init_connection_pool()
        await init_auto_commit_pool()
        await get_postgres_checkpointer()
        await init_portal_form_enrollment_graph()
    portal_form_enrollment_graph = SubgraphRegistry.get("portal_form_enrollment")
    return portal_form_enrollment_graph

_printed = set()

async def run_agent(queryString, uuid, graph, additional_arg=None):
    additional_arg = additional_arg or {}
    thread_id = uuid
    config = {
        "configurable": {
            "thread_id": thread_id,
        }
    }
    user_info = {"client_id": "Internal", "uuid": uuid}
    events = graph.astream(
        {
            "messages": ("user", queryString),
            "user_info": user_info,
            "is_multiagent": False,
            "is_auth_performed": True,
            "additional_arg": additional_arg,  
        },
        config,
        stream_mode="values"
    )
    async for event in events:
        response = _print_event(event, _printed)
    return response

async def main():
    graph = await start_portal_form_enrollment_graph()
    thread_id = str(uuid.uuid4())
    # additional_arg = {
    #     "client_id": "Internal",
    #     "uuid": "test-uuid-1234",
    #     "agent_name": "portal_form_enrollment",
    #     "application_name": "bneportal",
    # }
    try:
        while True:
            input_text = input("Enter your question or press 'q' to exit: ")
            if input_text.lower() in ["quit", "exit", "q"]:
                print("Goodbye! Have a nice day!")
                print("Exiting...")
                break
            response = await run_agent(input_text, thread_id, graph)
    finally:
        await close_connection_pools()

if __name__ == "__main__":
    asyncio.run(main())