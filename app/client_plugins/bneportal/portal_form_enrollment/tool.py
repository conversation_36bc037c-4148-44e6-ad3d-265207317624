from langchain_core.tools import tool
import traceback

@tool
async def process_enrollment(): # add arguments if required
    """Handles all queries related to enrollment. All queries related to enrollment should be routed to this tool."""

    try:
        return {
            "client": "bne_client",
            "response_type": "form_upload"
        }
    except Exception as e:
        raise Exception (traceback.format_exc())
    
