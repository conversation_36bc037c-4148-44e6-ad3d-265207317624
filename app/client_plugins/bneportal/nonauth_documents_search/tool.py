from langchain_core.tools import tool
from config.openAI import json_model, embeddings
from config.postgres import get_pg_connection, return_pg_connection
import json
from typing import Annotated
from langgraph.prebuilt.tool_node import InjectedState
from utils.tool_utils import log_tool_error
import traceback
from client_plugins.bneportal.bneportal_utils.utils import get_table_name_based_on_user_type, get_tile_options_for_user
from client_plugins.bneportal.bneportal_consts import NONAUTH_DOC_SEARCH_THRESHOLD

bne_training_material_fallback_message = "Sorry we cannot find the answer to your question. Please contact UHC Representative."

@tool
async def bne_training_document_search(state: Annotated[dict, InjectedState], bne_question: str): # add arguments if required
    "This tool provide capability of search and doing Q&A with non authenticated document and guides present in B&E portal."
    try:
        user_type = state.get("additional_arg", {}).get("bne_user_type")
        functionality = 'Training Materials'
        table_name = get_table_name_based_on_user_type(user_type, functionality)
        
        pg_connection = await get_pg_connection()
        # Establish cursor  
        cursor = pg_connection.cursor()  
        
        # Define similarity search query  
        similarity_search_query = f"""  
            SELECT   
                file_name,   
                file_url,   
                page_content,   
                created_at,   
                embedding <-> %s::vector AS score  
            FROM   
                {table_name}  
            ORDER BY   
                score  
            LIMIT 2  
        """  
        
        # Embed the question  
        question_embedding = await embeddings.aembed_query(bne_question)  
        
        # Execute the query with the embedding  
        await cursor.execute(similarity_search_query, (question_embedding,))  
        vector_results = await cursor.fetchall()  

        # Debug
        # for result in vector_results:
        #     print(result)
        #     print()
        # print()
        
        # Check if any results are returned  
        if not vector_results or len(vector_results) == 0:  
            await log_tool_error(state, "No documents found in DB for similarity search.", "document_search")
            return bne_training_material_fallback_message
        
        # Initialize variables
        vector_result_content = ""
        vector_results_file_url = ""
        most_similar_page = None
        threshold_score = NONAUTH_DOC_SEARCH_THRESHOLD
        
        # Filter results that meet the threshold criteria
        relevant_results = []
        for res in vector_results:
            file_name, file_url, page_content, created_at, score = res
            if score <= threshold_score:
                relevant_results.append(res)
        
        # If no results meet the threshold criteria, return fallback message
        if not relevant_results:
            return bne_training_material_fallback_message
        
        # Process only the relevant results that meet the threshold
        for res in relevant_results:
            file_name, file_url, page_content, created_at, score = res
            vector_result_content += page_content + "\n\n"
            
            # Initialize or update the most similar page based on the lowest score
            if not most_similar_page:  
                most_similar_page = {  
                    "score": score,  
                    "file_url": file_url  
                }  
            elif score < most_similar_page["score"]:
                most_similar_page = {
                    "score": score,
                    "file_url": file_url
                }
        
        # Assign the file URL of the most similar page
        vector_results_file_url = most_similar_page["file_url"]
        
        prompt = f"""
        Follow the given instructions and answer the question provided:
        Instructions:
        0. If the context has semantic meaning related to the question then only answer the question else return the message {bne_training_material_fallback_message}.
        1. If the user is asking to download a document, or for a document link, then answer the query and also give hyperlink generated using the file name and PDF link relevant to the question.
           - Take the first line from the document as a reference for the file name.
           - Ensure the hyperlink is properly formatted in Markdown syntax: `[File Name](Encoded URL)`.
           - Encode the URL to replace spaces with `%20` or any other special characters.
        2. Do not assume anything, do not hallucinate, and do not make up any information.
        3. Based on the context, respond with 3 follow-up questions.
        4. Make sure follow-up questions have semantic meaning related to the context and are short and precise.
        5. Follow-up questions should be phrased in first-person interrogative form, meaning they should reflect how a user would naturally ask a question in a conversational, direct, and task-oriented manner. Avoid formal or assistant-like phrasing such as "Would you like to see..." or "Can I help you with...". Instead, use concise, action-driven questions that mirror user intent.
        Examples:
        Instead of: "Would you like to see how to view saved reports?"
        Use: "How do I view saved reports?"
        Instead of: "Can I help you enroll a member?"
        Use: "How can I enroll a member?"
        Instead of: "Would you like to learn about report scheduling?"
        Use: "How do I schedule a report?"
        6. Respond in a JSON format with the following structure:
        {{
            "answer": "Your answer (should always be a string)",
            "follow_up_questions": ["Question 1", "Question 2", "Question 3"]
        }}

        Context: {vector_result_content}

        Question: {bne_question}

        PDF Link: {vector_results_file_url}

        Answer:
        """
        
        response = json_model.invoke(prompt).content
        json_response = json.loads(response)
        answer = json_response.get("answer", "") + "[view-source-hyper-link]" + vector_results_file_url
        follow_up_questions = json_response.get("follow_up_questions", [])
        if bne_training_material_fallback_message.lower() in answer.lower():
            return {
                "answer": bne_training_material_fallback_message,
                "follow_up_questions": []
            }
        return {
            "answer": answer,
            "follow_up_questions": follow_up_questions
        }

    except Exception as e:
        await pg_connection.rollback()
        await log_tool_error(state, traceback.print_exc(), "document_search")
        raise Exception (traceback.format_exc())
    finally:
        await pg_connection.commit()
        await cursor.close()
        await return_pg_connection(pg_connection)


@tool
async def bne_training_video_search(state: Annotated[dict, InjectedState], bne_question: str):
    """
    This tool provides access to training videos related to 'B&E'. It should be used whenever a user wants to watch or view training videos or any other video content related to 'B&E'.
    """
    try:
        user_type = state.get("additional_arg", {}).get("bne_user_type")
        functionality = 'Training Videos'
        table_name = get_table_name_based_on_user_type(user_type, functionality)
        # Connect with the database 
        pg_connection = await get_pg_connection()
        cursor = pg_connection.cursor()

        # Generate embedding for the question
        question_embedding = await embeddings.aembed_query(bne_question)
        
        # Perform similarity search using the embedding
        similarity_search_query = f"""
        SELECT file_url, embedding <-> %s::vector AS score 
        FROM {table_name}
        ORDER BY score
        LIMIT 1
        """
        await cursor.execute(similarity_search_query, (question_embedding,))
        result = await cursor.fetchone()
        
        if not result or len(result) == 0:
            return bne_training_material_fallback_message
        
        bne_training_video_url = result[0]
        
        if (not bne_training_video_url):
            return bne_training_material_fallback_message
        
        return {
            "client": "bne_client",
            "response_type": "video",
            "url": bne_training_video_url
        }
    
    except Exception as e:
        await pg_connection.rollback()
        await log_tool_error(state, traceback.print_exc(), "bne_training_video_search")
        raise Exception (traceback.format_exc())
    finally:
        await pg_connection.commit()
        await cursor.close()
        await return_pg_connection(pg_connection)


@tool
async def bne_training_tile_click(state: Annotated[dict, InjectedState], bne_question: str):
    """
    This tool provides very specific data related to exact user query strings 'Broker Training', 'Employer Training', and 'Internal Training'.
    Strictly: This tool should be used everytime a user asks a question that matches one of these strings: 'Broker Training', 'Employer Training', and 'Internal Training'.
    """
    try:
        user_type = state.get("additional_arg", {}).get("bne_user_type")
        
        return {
            "client": "bne_client",
            "response_type": "training_tile",
            "data": await get_tile_options_for_user(bne_question, user_type)
        }
    
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "bne_training_tile_click")
        raise Exception (traceback.format_exc())
