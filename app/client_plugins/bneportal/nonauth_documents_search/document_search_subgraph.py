from client_plugins.bneportal.nonauth_documents_search.main import nonauth_documents_search_tool_node
from client_plugins.bneportal.nonauth_documents_search.main import nonauth_documents_search_runnable
from client_plugins.bneportal.nonauth_documents_search.router import route_nonauth_documents_search_assistant, validate_response_for_human_intervention
from client_plugins.bneportal.bneportal_utils.utils import direct_to_human
from graph.nodes import Assistant
from langgraph.pregel import RetryPolicy
from graph.nodes import pop_dialog_state, escalation_fallback

from langgraph.graph import START
from graph.state import State, get_postgres_checkpointer
from utils.subgraph_builder import BaseSubgraphBuilder
from utils.subgraph_registry import SubgraphRegistry

nonauth_documents_search_compiled_graph = None

async def init_nonauth_documents_search_graph():
    """
    Initialize the nonauth documents search graph using the BaseSubgraphBuilder pattern
    and register it with SubgraphRegistry.
    """
    global nonauth_documents_search_compiled_graph
    if nonauth_documents_search_compiled_graph is None:
        # Create a new builder with fluent interface
        builder = BaseSubgraphBuilder(State)
        
        # Add nodes
        builder.add_node("nonauth_documents_search_assistant", Assistant(nonauth_documents_search_runnable), retry=RetryPolicy(max_attempts=2))
        builder.add_node("call_nonauth_documents_search_tool", nonauth_documents_search_tool_node)
        builder.add_node("leave_skill", pop_dialog_state)
        builder.add_node("escalation_fallback", escalation_fallback)
        builder.add_node("direct_to_human", direct_to_human)
        
        # Add edges
        builder.add_edge("leave_skill", "nonauth_documents_search_assistant")
        builder.add_edge("escalation_fallback", "nonauth_documents_search_assistant")
        builder.add_edge(START, "nonauth_documents_search_assistant")
        
        # Add conditional edges
        builder.add_conditional_edges("nonauth_documents_search_assistant", route_nonauth_documents_search_assistant)
        builder.add_conditional_edges("call_nonauth_documents_search_tool", validate_response_for_human_intervention)
        
        # Compile with async checkpointer
        nonauth_documents_search_compiled_graph = builder.compile(checkpointer=await get_postgres_checkpointer())
        
        # Register with SubgraphRegistry for future access
        SubgraphRegistry.register("nonauth_documents_search", nonauth_documents_search_compiled_graph)