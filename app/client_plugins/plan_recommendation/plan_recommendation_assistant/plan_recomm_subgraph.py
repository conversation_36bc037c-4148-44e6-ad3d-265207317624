from graph.nodes import plan_recommendation_tool_node
from graph.chains import plan_recommendation_runnable
from graph.routers import route_plan_recommendation_assistant
from graph.routers import route_escalation_fallback
from graph.nodes import Assistant
from langgraph.pregel import RetryPolicy
from graph.nodes import pop_dialog_state, escalation_fallback

from langgraph.graph import START
from graph.state import State, get_postgres_checkpointer
from utils.subgraph_builder import BaseSubgraphBuilder
from utils.subgraph_registry import SubgraphRegistry

plan_recomm_compiled_graph = None

async def init_plan_recomm_graph():
    """
    Initialize the plan recommendation graph using the BaseSubgraphBuilder pattern
    and register it with SubgraphRegistry.
    """
    global plan_recomm_compiled_graph
    if plan_recomm_compiled_graph is None:
        # Create a new builder with fluent interface
        builder = BaseSubgraphBuilder(State)
        
        # Add nodes
        builder.add_node("plan_recommendation_assistant", Assistant(plan_recommendation_runnable), retry=RetryPolicy(max_attempts=2))
        builder.add_node("call_plan_recommendation_tool", plan_recommendation_tool_node)
        builder.add_node("leave_skill", pop_dialog_state)
        builder.add_node("escalation_fallback", escalation_fallback)
        
        # Add edges
        builder.add_edge("leave_skill", "plan_recommendation_assistant")
        builder.add_edge("escalation_fallback", "plan_recommendation_assistant")
        builder.add_edge(START, "plan_recommendation_assistant")
        
        # Add conditional edges
        builder.add_conditional_edges("plan_recommendation_assistant", route_plan_recommendation_assistant)
        
        # Compile with async checkpointer
        plan_recomm_compiled_graph = builder.compile(checkpointer=await get_postgres_checkpointer())
        
        # Register with SubgraphRegistry for future access
        SubgraphRegistry.register("plan_recommendation", plan_recomm_compiled_graph)