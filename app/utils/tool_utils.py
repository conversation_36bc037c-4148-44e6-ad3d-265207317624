from langchain_core.messages import SystemMessage, ToolMessage
from services import log_error_service
from config.db import get_database
from services.event_logger_service import logger
from utils.general_utils import extract_values

def _print_event(event: dict, _printed: set, max_length=1500):
    current_state = extract_values(event.get("dialog_state"))
    if current_state:
        print("Currently in: ", current_state[-1])
    messages = event.get("messages")
    responses = [] 
    if messages:
        for msg in messages:
            if msg.id not in _printed and not isinstance(msg, SystemMessage) and not (isinstance(msg, ToolMessage) and msg.additional_kwargs.get("tool_escalation", False)):  
                msg_repr = msg.pretty_repr(html=True)  
                if len(msg_repr) > max_length:  
                    msg_repr = msg_repr[:max_length] + " ... (truncated)"  
                print(msg_repr)  
                _printed.add(msg.id)  
                responses.append(msg.content)
        return responses
    
    
async def log_tool_error(state, error, tool_name):
    user = state.get("user_info")
    db = await get_database(user["client_id"])
    await logger.error(user.get("uuid"), user.get("uuid").split("-")[0], user.get("client_id"), "tool", tool_name, str(state["messages"][-1]), None, None, str(error))
    await log_error_service.log_error_to_db(str(error), user["client_id"], user["uuid"], f'Tool Error - {tool_name}', db)
