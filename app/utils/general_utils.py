import json  
import traceback
import moment
from pytz import timezone 
import pdfplumber
import base64
from io import BytesIO
from reportlab.pdfgen import canvas 
from pypdf import PdfWriter, PdfReader
from services.zip_code_details_service import get_zip_details
from utils.helpers.constants import get_json
from utils.helpers.mappings import GENDER_MAPPING
from graph.prompts_and_capabilities import default_pdf_prompt
from fastapi.responses import JSONResponse

def extract_values(nested_list):  
    result = []  
    for item in nested_list:  
        if isinstance(item, list):  
            if len(item) == 0:  
                result.append('primary')  
            else:  
                result.extend(extract_values(item))  
        else:  
            result.append(item)  
    return result

def flatten_pdf_forms(pdf_file_like):
    input_pdf = PdfReader(pdf_file_like)  
    output_pdf = PdfWriter()  
  
    for page_number, page in enumerate(input_pdf.pages):  
        page_media_box = page.mediabox  
        packet = BytesIO()  
        c = canvas.Canvas(packet, pagesize=(page_media_box.upper_right[0], page_media_box.upper_right[1]))  
        if '/Annots' in page:  
            for annotation in page['/Annots']:  
                annot_object = input_pdf.get_object(annotation) 
                if '/T' in annot_object and '/V' in annot_object:  
                    field_name = annot_object['/T']  
                    field_value = annot_object['/V']  
                    if field_value == "/Off":  
                        continue  
                    if field_value == "/Yes":  
                        field_value = "x"    
                    rect = annot_object.get('/Rect', None)  
                    if rect and field_value:  
                        x, y = float(rect[0]), float(rect[1])  
                        y += (float(rect[3]) - float(rect[1])) / 2  
                        c.setFont("Helvetica", 12)  
                        c.drawString(x, y, str(field_value))  
                        # print(f"Drawing string: {field_value} at ({x}, {y})")  
        c.save()  
        packet.seek(0)  
  
        overlay_pdf = PdfReader(packet)  
        if len(overlay_pdf.pages) > 0:  
            page.merge_page(overlay_pdf.pages[0])  
        else:  
            print(f"Warning: No pages found in the overlay PDF for page {page_number}")    

        output_pdf.add_page(page)  

    output_stream = BytesIO()  
    output_pdf.write(output_stream)  
    output_stream.seek(0)  
    return output_stream.getvalue()


def convert_pdf_to_base64_images(pdf_bytes, resolution):  
    base64_images = []  
    flattened_pdf = flatten_pdf_forms((BytesIO(pdf_bytes))) if has_form_fields(pdf_bytes) else pdf_bytes

    with pdfplumber.open(BytesIO(flattened_pdf)) as pdf:  
        for page in pdf.pages:  
            page_image = page.to_image(resolution=resolution)  
            pil_image = page_image.original 
            buffered = BytesIO()  
            pil_image.save(buffered, format="PNG")  
            img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')  
            base64_images.append(img_base64) 

            # Save the Image to check the quality - uncomment these two lines to do so
            # image_path = f"page_{page}.png"
            # pil_image.save(image_path)

    return base64_images 

def getTime():
    utc_time = moment.utcnow()  
    new_york_tz = timezone('America/New_York')  
    new_york_time = utc_time.clone().timezone(new_york_tz.zone) 
    return str(new_york_time)
  

def has_form_fields(pdf_bytes):  
    pdf = PdfReader(BytesIO(pdf_bytes))  
    for page in pdf.pages:  
        if '/Annots' in page:  
            for annotation in page['/Annots']:  
                annot_object = pdf.get_object(annotation)  
                if annot_object.get('/Subtype') == '/Widget':  # Check if it's a form field widget  
                    return True  
    return False  


def is_json(chatbot_response):  
    try:  
        json_object = json.loads(chatbot_response)  
    except ValueError as e:  
        return False  
    return True  

async def is_hw_json(chatbot_response):
    memberList = ["EmployeeInfo", "SpouseInfo", "Dependent1Info", "Dependent2Info", "Dependent3Info", "Dependent4Info"]

    try:
        json_object = json.loads(chatbot_response) 
        hw_json = get_json()
        for key in hw_json.keys():
            if key not in json_object.keys():
                return False
        for member in memberList:
            for key in hw_json[member].keys():
                if key not in json_object[member].keys():
                    return False
    except:
        return False
    return True

def parse_response(response):  
    if is_json(response):  
        return response  
    else:
        try:   
            start = response.find("```json") + len("```json")  
            end = response.find("```", start)  
            json_str = response[start:end].strip()  
            if is_json(json_str):  
                return json_str  
            else:  
                return "Please retry as we encountered a formatting issue"
        except Exception as e:  
            print(str(traceback.format_exc()))
            raise Exception(traceback.format_exc())
        
        
async def get_gpt_message_object(input, prompt=None, resolution=200, pdf_data=False):
    
    if type(input) is str:
        messages = {  
                     "role": "user",  
                     "content": input
                } 
        return messages

    messages = [
        {
            "name": "images",
            "role": "user" if pdf_data else "system",
            "content": [
                {
                    "type": "text",
                    "text": prompt if prompt else default_pdf_prompt
                }
            ]
        }  
    ]

    if pdf_data:
        base64_images = convert_pdf_to_base64_images(input, resolution)
        for img in base64_images:  
            messages[0]["content"].append({"type": "image_url", "image_url": {"url": f"data:image/png;base64,{img}"}})
        
    return messages

def convert_date_format(date):
    new_date_format = date[6:] + '-' + date[:2] + '-' + date[3:5]
    return new_date_format

def format_gender(gender):
    
    gender_lower = gender.lower()

    for standard, variations in GENDER_MAPPING.items():
        if gender_lower in variations:
            return standard.capitalize()

    return None

async def map_final_response(response):
    if not isinstance(response, list):
        response = [response]
    modified_responses = []
    for resp in response:
        original_resp = resp
        chat_resp_msg_for_json = "I hope this answer aligns with your specific needs."
        try:
            content = resp
            if isinstance(resp, JSONResponse):
                content = resp.body.decode() if hasattr(resp, 'body') else str(resp)
                
            parsed_json = json.loads(content)
            
            if "response_type" in parsed_json:
                chat_resp_msg_for_json = make_response_msg(parsed_json["response_type"])
                                                                       
            if await is_hw_json(content):
                content = {"heavyweightPayloadJson": json.loads(content)}
                
            if isinstance(parsed_json, dict) and "answer" in parsed_json.keys() and "follow_up_questions" in parsed_json.keys():
                if isinstance(parsed_json["answer"], dict):
                    modified_responses.append({
                        "chatRespMsg": "I hope this answer aligns with your specific needs.",
                        "chatRespJson": json.dumps(parsed_json["answer"]),
                        "follow_up_questions": parsed_json["follow_up_questions"]
                    })
                else:
                    modified_responses.append({
                        "chatRespMsg": parsed_json["answer"],
                        "follow_up_questions": parsed_json["follow_up_questions"]
                    })
            elif isinstance(parsed_json, dict) and "answer" in parsed_json.keys() and "custom_suggestions" in parsed_json.keys():
                modified_responses.append({
                    "chatRespMsg": parsed_json["answer"],
                    "custom_suggestions": parsed_json["custom_suggestions"]
                })
            else:
                modified_responses.append({"chatRespMsg": chat_resp_msg_for_json, "chatRespJson": content})

        except (json.JSONDecodeError, TypeError):
            if resp != "":
                if isinstance(original_resp, JSONResponse):
                    content = original_resp.body.decode() if hasattr(original_resp, 'body') else str(original_resp)
                    modified_responses.append({"chatRespMsg": content})
                else:
                    modified_responses.append({"chatRespMsg": resp})

    return modified_responses

async def update_hw_json(json_data, parameters):  
        (groupId, effectiveDate, employBeginDate, firstName, lastName, SSN, DOB, gender,employeeAddress, postalCode,   
         qualifyingEventDate, planAndPopulationDetails, billGroupDetails, dependents, nameMiddle, phoneNumber,   
         communicationText) = parameters  
          
        zip_details = await get_zip_details(postalCode)
  
        for detail in planAndPopulationDetails:  
            if 'selectedPopulation' in detail:
                selected_population = detail['selectedPopulation']  
                population_list =  [] 
                if selected_population: 
                    population_list.append(selected_population)  
                detail['populationList'] = population_list


        json_data["EffectiveDate"] = convert_date_format(effectiveDate)  
        json_data["QualifyingEffectiveDate"] = convert_date_format(qualifyingEventDate)  
        json_data["GroupNumber"] = groupId  
        json_data["EmployeeInfo"].update({  
            "FirstName": firstName,  
            "LastName": lastName,  
            "MiddleName": nameMiddle,  
            "EmployeeType": "Active",  
            "SSN": SSN,  
            "DOB": convert_date_format(DOB),  
            "Gender": format_gender(gender),  
            "DateOfHire": convert_date_format(employBeginDate),  
            "City": zip_details["city"] if zip_details else None,  
            "State": zip_details["State_code"] if zip_details else None,  
            "Address": employeeAddress,
            "Country": "US",
            "PostalCode": postalCode,  
            "HomePhone": phoneNumber,  
            "EmailAddress": communicationText,  
            "PlanAndPopulationDetails": planAndPopulationDetails,
            "billGroupList": [billGroupDetails], 
            "SelectedBillGroup": billGroupDetails,  
        })  
        
        def update_dependent_info(existing_info, new_info):
            for key, value in new_info.items():
                    if key.lower() == 'type':
                        continue
                    if key == 'DOB':
                        value = convert_date_format(value)
                    if key == 'Gender':
                        value = format_gender(value)
                    existing_info[key] = value
            existing_info['PlanAndPopulationDetails'] = planAndPopulationDetails
            return existing_info

        spouse = next((dep for dep in dependents if dep.get('Type').lower() == 'spouse'), None)
        if spouse:
            json_data['SpouseInfo'] = update_dependent_info(json_data.get('SpouseInfo', {}), spouse)

        for i, child in enumerate((dep for dep in dependents if dep.get('Type').lower() == 'child'), 1):
            if i > 4:
                break
            
            key = f'Dependent{i}Info'
            json_data[key] = update_dependent_info(json_data.get(key, {}), child)
    
        return json_data  

def remove_nested_objects(json_obj):
    # These keys are those that bring the JSON object's depth value to 3. We will move them up one level in the JSON
    depth_three_keys = []
    depth_three_values = []

    for key, value in json_obj.items():
        if isinstance(value, list):
            # Lists of strings or numbers are ok here. Lists of lists or dicts aren't
            list_or_dict = [item for item in value if isinstance(item, list) or isinstance(item, dict)]
            if len(list_or_dict) > 0:
                depth_three_keys.append(key)
                depth_three_values.append(value)

        elif isinstance(value, dict):
            depth_three_keys.append(key)
            depth_three_values.append(value)

    stored_key_values = {}

    for i in range(len(depth_three_keys)):
        # These keys bring the depth value to 4 and will be removed
        depth_four_list_indices = []
        depth_four_keys = []

        if isinstance(depth_three_values[i], list):
            for j in range(len(depth_three_values[i])):
                if isinstance(depth_three_values[i][j], list) or isinstance(depth_three_values[i][j], dict):
                    depth_four_list_indices.append(j)
            depth_three_values[i] = [item for index, item in enumerate(depth_three_values[i]) if index not in depth_four_list_indices]

        elif isinstance(depth_three_values[i], dict):
            for key, value in depth_three_values[i].items():
                if isinstance(value, list) or isinstance(value, dict):
                    depth_four_keys.append(key)
            for key in depth_four_keys:
                depth_three_values[i].pop(key)

        stored_key_values[depth_three_keys[i]] = depth_three_values[i]

    # Remove depth_three_keys from the JSON object for now; they are saved inside stored_key_values and will be added back in later, one level above where they were initially
    for key in depth_three_keys:
        json_obj.pop(key)

    return json_obj, stored_key_values

def trim_json_response(json_response):
    list_of_lists_to_append = []
    list_of_dicts_to_append = []

    for value in json_response.values():
        if isinstance(value, list):
            list_to_append = []
                    
            # Convert any non-dict values in lists to dicts (to properly display them in the UI)
            non_dict_list = [item for item in value if not isinstance(item, dict)]
            for item in non_dict_list:
                value.remove(item)
                item = {"data": item}
                value.append(item)

            for item in value:
                item, dict_to_append = remove_nested_objects(item)
                list_to_append.append(dict_to_append)
                list_of_lists_to_append.append(list_to_append)

        elif isinstance(value, dict):
            value, dict_to_append = remove_nested_objects(value)
            list_of_dicts_to_append.append(dict_to_append)

    for i in range(len(list_of_dicts_to_append)):
        for key, value in list_of_dicts_to_append[i].items():
            json_response[key] = value
    
    return json_response

def validate_tool_call(generated_tool_list, tool_list):   
    tool_names_from_dicts = {item['name'] for item in generated_tool_list}  

    tool_names = {  
        t.__name__ if isinstance(t, type) else t.name  
        for t in tool_list  
    }  

    return tool_names_from_dicts.issubset(tool_names) 


def all_assistants_are_same(tool_calls):  
    names = [call['name'] for call in tool_calls]  
    return all(name == names[0] for name in names)

def make_response_msg(response_type):
    if response_type == "form_upload":
        return "Would you like to upload an enrollment form to auto-fill this enrollment?"
    else:
        return "I hope this answer aligns with your specific needs."
