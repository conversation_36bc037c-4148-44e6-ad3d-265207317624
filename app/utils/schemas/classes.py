from pydantic import BaseModel
from typing import Optional, List, TypedDict

class generateTitleInterface(BaseModel):
    user_input: str
    user_name: str
    chatbot_response: Optional[str] = None
    edit: Optional[bool] = False
    uuid: str
    filename: Optional[str] = None
    session_id: str

class getTitleInterface(BaseModel):
    user_name: str
    uuid: str
    session_id: str

class getChatInterface(BaseModel):
    uuid: str
    offset: int
    limit: int
    user_name: str
    session_id: str

class saveChatInterface(BaseModel):
    title: str
    user_name: str
    conversation: list
    uuid: str
    offset: int
    session_id: str




class fileUploadInterface(BaseModel):
    file: object

class Theme(BaseModel):
    user_name: str
    theme: str
    uuid: str
    session_id: str

class removeConvoInterface(BaseModel):
    user_name: str
    uuid: str
    session_id: str

class GetTheme(BaseModel):
    user_name: str
    uuid: str
    session_id: str

class NYS45Employee(TypedDict):
    socialSecurityNumber: str
    name: str
    totalRemuneration: str
    grossWages: str
    totalTax: str

class NYS45Data(TypedDict):
    formName: str
    employerName: str
    employerRegistrationNumber: Optional[str] = ""
    withholdingIdentificationNumber: str
    quarterDates: str
    year: str
    employeeList: List[NYS45Employee]
