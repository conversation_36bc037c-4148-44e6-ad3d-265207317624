from pydantic import BaseModel
from typing import Optional, List, TypedDict

class generateTitleInterface(BaseModel):
    user_input: str
    msid: str
    id: int
    chatbot_response: Optional[str] = None
    edit: Optional[bool] = False
    uuid: str
    filename: Optional[str] = None

class getTitleInterface(BaseModel):
    msid: str
    uuid: str

class getChatInterface(BaseModel):
    convoId: int
    uuid: str
    offset: int
    limit: int

class saveChatInterface(BaseModel):
    id: int
    title: str
    msid: str
    conversation: list
    uuid: str
    offset: int

class UserData(BaseModel):
    index: int
    subindex: int
    submittedData: str
    chatbotResponse: dict
    feedback: str

class userFeedbackInterface(BaseModel):
    id: int
    msid: str
    uuid: str
    data: UserData

class fileUploadInterface(BaseModel):
    file: object

class Theme(BaseModel):
    msid: str
    theme: str
    uuid: str

class removeConvoInterface(BaseModel):
    id: int
    msid: str
    uuid: str

class GetTheme(BaseModel):
    msid: str
    uuid: str

class NYS45Employee(TypedDict):
    socialSecurityNumber: str
    name: str
    totalRemuneration: str
    grossWages: str
    totalTax: str

class NYS45Data(TypedDict):
    formName: str
    employerName: str
    employerRegistrationNumber: Optional[str] = ""
    withholdingIdentificationNumber: str
    quarterDates: str
    year: str
    employeeList: List[NYS45Employee]
