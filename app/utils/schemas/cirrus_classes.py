from pydantic import BaseModel
from typing import Optional, List
from typing_extensions import TypedDict

class CirrusMemberSearch(BaseModel):
    memGroupID: str
    firstNameStartsWith: Optional[str] = ""
    lastNameStartsWith:  Optional[str] = ""
    SSN: Optional[str] = ""
    clientId: str
    uuid: str
    
class ContractOptionsAndGroupSearchInterface(BaseModel):
    memberGroupID: str
    clientId: str
    uuid: str

class Plan(BaseModel):
    planName: str
    coverageType: str

class SimilarPlanList(BaseModel):
    similarPlans: List[Plan]