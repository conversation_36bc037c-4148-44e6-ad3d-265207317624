from langgraph.graph import StateGraph
from utils.agent_utils import create_entry_node
from graph.nodes import Assistant
from langgraph.pregel import RetryPolicy
from services.event_logger_service import logger
from graph.state import get_postgres_checkpointer

def add_assistant_nodes(builder: StateGraph, assistant_name, assistant_runnable, tool_node, add_extra_tool_node, extra_tool_suffix, add_entry_node):
    
    if add_entry_node:
        builder.add_node(f"enter_{assistant_name}_assistant", create_entry_node(f"{assistant_name} assistant", f"{assistant_name}_assistant"))
    
    builder.add_node(f"{assistant_name}_assistant", Assistant(assistant_runnable), retry=RetryPolicy(max_attempts=2))
    
    builder.add_node(f"call_{assistant_name}_tool", tool_node)

    if add_extra_tool_node:
        builder.add_node(f"call_{assistant_name}_tool{extra_tool_suffix}", tool_node)
        
        
def add_edges (builder: StateGraph, edges_registry):
    for edge in edges_registry:
        start = edge["start"]
        end = edge["end"]
        
        if edge["is_conditional"]:
            builder.add_conditional_edges(start, end)
        else:
            builder.add_edge(start, end)
            
            
async def delete_dummy_thread(session_id: str, client_id: str):
    """Delete thread and checkpoints if the first segment of the session_id is "dummy" (case-insensitive)."""
    if session_id and session_id == "dummy":
        try:
            postgres_checkpointer = await get_postgres_checkpointer()
            await postgres_checkpointer.adelete_thread(session_id)
            print(f"Thread with session_id {session_id} deleted successfully.")
            await logger.info(None, None, session_id, 
                            client_id, 
                            "system", 
                            "database", 
                            "delete_thread", 
                            {"thread_id": session_id}, 
                            "Thread and checkpoints deleted", 
                            200, 
                            None)
        except Exception as e:
            print(f"Error deleting thread with session_id {session_id}: {e}")
            await logger.error(None, None, session_id, 
                            client_id, 
                            "system", 
                            "database", 
                            "delete_thread", 
                            {"thread_id": session_id}, 
                            None, 
                            500, 
                            str(e))