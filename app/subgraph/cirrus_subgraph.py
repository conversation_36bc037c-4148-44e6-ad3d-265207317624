from graph.nodes import cirrus_assistant_tool_node
from graph.chains import cirrus_assistant_runnable
from graph.routers import route_cirrus_assistant, route_escalation_fallback
from graph.nodes import Assistant
from langgraph.pregel import RetryPolicy
from graph.nodes import pop_dialog_state, escalation_fallback

from langgraph.graph import START
from graph.state import State, get_postgres_checkpointer
from utils.subgraph_builder import BaseSubgraphBuilder
from utils.subgraph_registry import SubgraphRegistry

cirrus_compiled_graph = None

async def init_cirrus_graph():
    """
    Initialize the cirrus graph using the BaseSubgraphBuilder pattern
    and register it with SubgraphRegistry.
    """
    global cirrus_compiled_graph
    if cirrus_compiled_graph is None:
        # Create a new builder with fluent interface
        builder = BaseSubgraphBuilder(State)
        
        # Add nodes
        builder.add_node("cirrus_assistant", Assistant(cirrus_assistant_runnable), retry=RetryPolicy(max_attempts=2))
        builder.add_node("call_cirrus_tool", cirrus_assistant_tool_node)
        builder.add_node("call_cirrus_tool_with_assistant", cirrus_assistant_tool_node)
        builder.add_node("leave_skill", pop_dialog_state)
        builder.add_node("escalation_fallback", escalation_fallback)
        
        # Add edges
        builder.add_edge("leave_skill", "cirrus_assistant")
        builder.add_edge("escalation_fallback", "cirrus_assistant")
        builder.add_edge(START, "cirrus_assistant")
        builder.add_edge("call_cirrus_tool_with_assistant", "cirrus_assistant")
        
        # Add conditional edges
        builder.add_conditional_edges("cirrus_assistant", route_cirrus_assistant)
        
        # Compile with async checkpointer
        cirrus_compiled_graph = builder.compile(checkpointer=await get_postgres_checkpointer())
        
        # Register with SubgraphRegistry for future access
        SubgraphRegistry.register("cirrus", cirrus_compiled_graph)        
