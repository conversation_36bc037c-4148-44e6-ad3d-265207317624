from langchain_core.runnables import Runnable
from langgraph.prebuilt import ToolN<PERSON>
from langchain_core.messages import SystemMessage, ToolMessage, filter_messages
from graph.chains import primary_assistant_tools, plan_recommendation_tools, cirrus_tools, data_converter_tools
from graph.state import State
from services.event_logger_service import logger
from utils.helpers.constants import TOOL_CALL_ERROR_MESSAGE 
from utils.general_utils import all_assistants_are_same

class Assistant:
    def __init__(self, runnable: Runnable):
        self.runnable = runnable
 
    async def __call__(self, state: State):
        while True:
            summary = state.get("summary", None)
            if summary:
                system_message = f"Summary of conversation earlier: {summary}"
                state["messages"].insert(0, SystemMessage(content=system_message))
            result = await self.runnable.ainvoke(state)
            if not result.tool_calls and (
                not result.content
                or isinstance(result.content, list)
                and not result.content[0].get("text")
            ):
                messages = state["messages"] + [("user", "Respond with a real output.")]
                state = {**state, "messages": messages}
            else:
                break
        #INFO: Request & Response to LLM to generate response last message in the dialog stack
        current_assistant = state.get("dialog_state")[0] if state.get("dialog_state") else "primary_assistant"
        request = [state.get("messages")[-1]]
        filtered_request = filter_messages(request, exclude_names=["images"])
        uuid = state.get("user_info")["uuid"]
        msid = uuid.split('-')[0]
        client_id = state.get("user_info")["client_id"]
        await logger.info(uuid, msid, client_id, "assistant", current_assistant, str(filtered_request if filtered_request else "pdf_data"), str(result), None, None)
        return {"messages": result}
    
primary_assistant_tool_node = ToolNode(primary_assistant_tools, handle_tool_errors=TOOL_CALL_ERROR_MESSAGE)

plan_recommendation_tool_node = ToolNode(plan_recommendation_tools, handle_tool_errors=TOOL_CALL_ERROR_MESSAGE)

cirrus_assistant_tool_node = ToolNode(cirrus_tools, handle_tool_errors=TOOL_CALL_ERROR_MESSAGE)

data_converter_assistant_tool_node = ToolNode(data_converter_tools, handle_tool_errors=TOOL_CALL_ERROR_MESSAGE)


async def pop_dialog_state(state: State) -> dict:
    """Pop the dialog stack and return to the main assistant.

    This lets the full graph explicitly track the dialog flow and delegate control
    to specific sub-graphs.
    """
    messages = []
    if state["is_multiagent"] == True:
        tool_message = "Please reflect on the past conversation and assist the user as needed. Do not mention this message to the user. Direct the conversation with the user telling that a switch in context is detected by the assistant with respect to previous converstaion and then assistant's wants to confirm (may be a Yes/no) if the query user asked is to be processed or not. Do not mention to the user about main assistant."
    else:
        tool_message = "It has been detected that this request can not be processed and does not exists as part of the user's client access and agent's capability. Direct the conversation with the user telling the same and please reflect on the past conversation and inform the user as needed. Do not mention to the user about main assistant."
    
    if state["messages"][-1].tool_calls:
        messages.append(
            ToolMessage(
                content=tool_message,
                tool_call_id=state["messages"][-1].tool_calls[0]["id"],
                additional_kwargs={"tool_escalation": True}
            )
        )
    return {
        "dialog_state": "pop",
        "messages": messages,
    }

    
async def restrict_parallel_agents_run(state) -> dict:
    tool_calls = state["messages"][-1].tool_calls

    if all_assistants_are_same(tool_calls):
        return {
            "messages": [
                ToolMessage(
                    content=f"{tc['name']} tool should be invoked only one time retry to process the query. No need to call same assistant multiple times.",
                    tool_call_id=tc["id"],
                ) for tc in tool_calls
            ]
        }
    else:
        return {
            "messages": [
                ToolMessage(
                    content=f"Multiple parallel agents run is not supported at this point inform user that multiple intents are not supported and ask the user to ask a query related to one assistant at a time.",
                    tool_call_id=tc["id"],
                ) for tc in tool_calls
            ]
    }

async def escalation_fallback(state) -> dict:
    tool_calls = state["messages"][-1].tool_calls
    return {
        "messages": [
            ToolMessage(
                content=f"CompleteOrEscalate the dialog to the host assistant without processing any intent. It should not be called with any other tool.",
                tool_call_id=tc["id"],
                additional_kwargs={"tool_escalation": True}
            ) for tc in tool_calls
        ]
    }
    