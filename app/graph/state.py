from typing import Annotated, Literal
from typing_extensions import TypedDict
from langgraph.graph.message import AnyMessage, add_messages
from utils.agent_utils import update_dialog_stack
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.checkpoint.memory import InMemorySaver
import asyncio
import os

lock = asyncio.Lock()
is_in_memory_checkpointer = os.getenv('IN_MEMORY_CHECKPOINTER', None) == 'true'

#NOTE: This INMemorySaver may cause severe issue for ASYNC app and should never be used in deployment.
in_memory_checkpointer = InMemorySaver()
if is_in_memory_checkpointer:
    print("-"*123)
    print("| Using InMemorySaver for checkpointer. This is not recommended for deployment use. May cause severe issues in ASYNC app. |")
    print("-"*123)

class State(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]
    user_info: dict
    pdf_data: str
    user_permissions: dict
    authorized_intents: dict
    return_to_user: bool
    review_payload: dict
    additional_arg: dict
    summary: str
    is_multiagent: bool
    is_auth_performed: bool
    dialog_state: Annotated[
        list[
            Literal[
                "assistant",
                "cirrus_assistant",
                "plan_recommendation_assistant",
                "data_converter_assistant",
                "surest_assistant",
                "samxone_assistant",
                "portal_form_enrollment_assistant",
                "nonauth_documents_search_assistant",
                "gco_reporting_assistant"
            ]
        ],
        update_dialog_stack,
    ]
    
postgres_checkpointer = None
connection_tested = False

async def get_postgres_checkpointer():
    
    #-------Temporary code not to be used in production-------
    if is_in_memory_checkpointer:
        return in_memory_checkpointer
    #---------------------------------------------------------
    
    global postgres_checkpointer, connection_tested
    
    from config.postgres import auto_commit_pool
    
    if auto_commit_pool is None:
        raise ValueError("Cannot initialize postgres_checkpointer: auto_commit_pool is None")
    
    if not connection_tested:
        try:
            async with await auto_commit_pool.getconn() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT 1")
                    await cursor.fetchone()
            connection_tested = True
        except Exception as e:
            print(f"DB connection pool error: {e}")
            # Recreate the connection pool
            from config.postgres import init_auto_commit_pool
            await init_auto_commit_pool()
            connection_tested = True  
            
    # Create or get the existing checkpointer
    async with lock:
        if postgres_checkpointer is None:
            postgres_checkpointer = AsyncPostgresSaver(auto_commit_pool)
        
    return postgres_checkpointer
