from langchain_core.messages import AIMessage, filter_messages, HumanMessage
import traceback
from config.openAI import model
from graph.auth_nodes.constants import AUTHORIZATION_INTENT_PARSING_SYSTEM_PROMPT, GENERAL_NO_AUTH_INTENT_CATEGORIES
from graph.auth_nodes.helpers import permissions_map, convert_permissions_to_intents
from graph.auth_nodes.authorization_classes import AuthorizationDetails, Result
from graph.state import State
from typing import List, Dict, Any, <PERSON><PERSON>
import httpx
import asyncio
import time  
import os
from asyncio import Lock

BNE_GRAPHQL_API_ENDPOINT = os.getenv("BNE_GRAPHQL_API_ENDPOINT")
BNE_OAUTH2_TOKEN_URL = os.getenv("BNE_OAUTH2_TOKEN_URL")
BNE_CLIENT_ID = os.getenv("BNE_CLIENT_ID")
BNE_CLIENT_SECRET = os.getenv("BNE_CLIENT_SECRET")
BNE_SCOPE= os.getenv("BNE_SCOPE")
TOKEN = None  
TOKEN_EXPIRY = 0
token_lock = Lock()

permissions_cache = {}  
cache_lock = Lock() 

async def parse_user_messages(user_msgs: list[str]):
    messages = [
    {
        "role": "system",
        "content": "Use these instructions to respond to the user:\n" + AUTHORIZATION_INTENT_PARSING_SYSTEM_PROMPT
    },
]
    messages.append(
        {
            "role": "user",
            "content": f"These are the user's conversation:\n{user_msgs}\nFocus mostly on the most recent messages, the ones near the end of the list."
        }
    )
    llm_str = model.with_structured_output(AuthorizationDetails)
    resp = await llm_str.ainvoke(messages)  
    
    return resp

async def get_new_token() -> Tuple[str, int]:  
    try:  
        async with httpx.AsyncClient() as client:
            response = await client.post(
                BNE_OAUTH2_TOKEN_URL, 
                data={  
                    'grant_type': 'client_credentials',  
                    'client_id': BNE_CLIENT_ID,  
                    'client_secret': BNE_CLIENT_SECRET,
                    'scope': BNE_SCOPE
                }
            )  
            response.raise_for_status()  
            token_data = response.json()  
            return token_data['access_token'], token_data['expires_in']  
    except httpx.RequestError as e:  
        print(f"Error fetching new token: {e}")  
        raise  
  
async def ensure_token():  
    global TOKEN, TOKEN_EXPIRY
    
    async with token_lock: 
        if time.time() >= TOKEN_EXPIRY:  
            TOKEN, expires_in = await get_new_token()  
            TOKEN_EXPIRY = time.time() + expires_in 
        

async def fetch_permissions(permission_code: str, auth_details: Dict[str, Any], ohid_uuid: str) -> Any:  
    return True  #NOTE: Mocked response for till we get the actual response from BNE
    await ensure_token()  
      
    query = """  
    query CheckUserPermissions(
    $identifier: UserIdentifierInput!
    $data: CheckUserPermissionsInput!
    ) {
    checkUserPermissions(identifier: $identifier, data: $data) {
        hasPermission
       
    }
    }
    """    
    variables = {
    "identifier": {
        "type": "OHID",
        "value": ohid_uuid
    },
    "data": {
        "matchType": "ALL",
        "permissions": [permission_code],
        "scopeId": auth_details.get("group_id"),
        "scopeType": "GROUP"
    }
    }  
    
    headers = {  
        'Content-Type': 'application/json',  
        'Authorization': f'Bearer {TOKEN}',
        'x-upstream-env': 'stage',
        'User-Agent': 'lingo',  
        'Accept': '*/*',  
        'Accept-Encoding': 'gzip, deflate, br',  
        'Connection': 'keep-alive',
    }  
    
    try:  
        async with httpx.AsyncClient() as client:
            response = await client.post(
                BNE_GRAPHQL_API_ENDPOINT, 
                json={'query': query, 'variables': variables}, 
                headers=headers
            ) 
            response.raise_for_status()
            return response.json()['data']['checkUserPermissions']['hasPermission'] 
    except httpx.RequestError as e:  
        print(f"Error fetching permissions: {e}")  
        return False
  
async def get_permission(code: str, auth_detail_dict: Dict[str, Any], ohid_uuid: str, msid: str) -> Any:   
    cache_key = (msid, code, tuple(sorted(auth_detail_dict.items())))  
  
    async with cache_lock:
        if cache_key in permissions_cache:  
            return permissions_cache[cache_key]
    
    permission = await fetch_permissions(code, auth_detail_dict, ohid_uuid)  
    print("Permission fetched from API:", permission)  
    
    async with cache_lock:
        permissions_cache[cache_key] = permission  
    
    return permission  


def generate_auth_details(result: Result) -> Dict[str, List[Dict[str, Any]]]:  
  
    auth_details = {}  
      
    for intent in result.intents:  
        if intent.category in GENERAL_NO_AUTH_INTENT_CATEGORIES:  
            continue  
          
        for subcategory in intent.subcategories:  
            if subcategory.details_map and subcategory.is_auth_required:  
                code = permissions_map.get(subcategory.name, None)  
                if code:  
                    if code not in auth_details:  
                        auth_details[code] = []  
                    auth_details[code].append(subcategory.details_map)  
                else:  
                    raise ValueError(f"Unrecognized subcategory name: {subcategory.name}")  
      
    return auth_details 

  
async def check_permissions(auth_details: Dict[str, List[Dict[str, Any]]], ohid_uuid: str, msid: str) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:  
    allowed_permissions = {}  
    denied_permissions = {}  
    
    permission_tasks = []
    for code, details_list in auth_details.items():  
        allowed_permissions[code] = []  
        denied_permissions[code] = []  
  
        for details in details_list:  
    
            task = asyncio.create_task(get_permission(code, details, ohid_uuid, msid))
            permission_tasks.append((code, details, task))
    
    for code, details, task in permission_tasks:
        permission = await task
        if permission:  
            allowed_permissions[code].append(details)  
        else:  
            denied_permissions[code].append(details)  
  
    return {  
        'allowed_permissions': allowed_permissions,  
        'denied_permissions': denied_permissions  
    } 

async def bne_authorization_check(state: State, auth_res: AuthorizationDetails):
    """Performs BnE authorization for the user's query. 
    """
    try:
        msid = state["user_info"]["uuid"].split("-")[0]
        ohid_uuid = "adffcd9b-efe7-40ec-bf8d-6f28e316401f" #Will get from state["user_info"]["ohid_uuid"] in future
        
        authorization_data = generate_auth_details(auth_res) 
        permissions_check = await check_permissions(authorization_data, ohid_uuid, msid)  
        
        allowed_permissions = permissions_check['allowed_permissions']  
        denied_permissions = permissions_check['denied_permissions']  
        
        all_denied = all(not details for details in allowed_permissions.values())  
        # messages = []  
    
        if all_denied:  
            return {"return_to_user": True, "messages": [AIMessage(content="Sorry, you are not authorized to perform this action. Please contact your administrator for further assistance.")]} 
        else:  
            # allowed_actions = "\n".join(  
            #     f"Action: {key.replace('_', ' ').title()}, Details: {details}"  
            #     for key, code in permissions_map.items()  
            #     for details in allowed_permissions.get(code, []) if details  
            # )  
            
            authorized_intents = convert_permissions_to_intents(allowed_permissions, use_tool_names=True)
                                
            denied_actions = "\n".join(  
                f"Action: {key.replace('_', ' ').title()}, Details: {details}"  
                for key, code in permissions_map.items()  
                for details in denied_permissions.get(code, []) if details  
            )   
            if denied_actions:  
                # messages.append(f"Sorry, you are not authorized to perform following action/s: {denied_actions}. Please contact your administrator for further assistance.")  
                return {"return_to_user": True, "messages": [AIMessage(content=f"Sorry, you are not authorized to perform following action/s: {denied_actions}. Please contact your administrator for further assistance.")]} 
            # if allowed_actions:  
            #     messages.append(f"User is only authorized to perform the following actions: {allowed_actions}") 
    
            return {  
                "return_to_user": False,
                'is_auth_performed': True,
                'authorized_intents': authorized_intents,
            }  
            
    except Exception:
        raise Exception (traceback.format_exc())


async def authorize_and_route_to_workflow(state: State):
    """Consumes a user's query and determines whether or not the user requires authorization to access resources based on the query's intent.
    """
    try:
        if state.get('is_auth_performed', None):
            print("-"*100)
            print("Skipping authorization check because either the AUTH is disabled or has already been completed at the parent level.")
            print("-"*100)
            return
        
        messages = state.get("messages")
        filtered_user_conversation = list(filter_messages(messages, include_types=[HumanMessage, AIMessage], exclude_names=["images"], exclude_tool_calls=True))
        content_with_identifiers = [(type(message).__name__, message.content) for message in filtered_user_conversation] 

        auth_res = await parse_user_messages(content_with_identifiers)
        print("Authorization Intents: ", auth_res)
        if auth_res.intents is None or auth_res.error is not None:
            return {"return_to_user": True, "messages": [AIMessage(content="Sorry, I was unable to determine the intent of your query. Could you try again to explain what you need help with?")]}
    
        if not auth_res.is_auth_required:
            return {"return_to_user": False, 'is_auth_performed': True}
        
        if auth_res.is_data_required_from_user:
            return {"return_to_user": True, "messages": [AIMessage(content = auth_res.message)]}
        else:
            return await bne_authorization_check(state, auth_res)

    except Exception:
        raise Exception (traceback.format_exc())