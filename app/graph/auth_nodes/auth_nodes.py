from langchain_core.messages import AIMessage, filter_messages, HumanMessage
import traceback
from config.openAI import model
from graph.auth_nodes.constants import GENERAL_NO_AUTH_INTENT_CATEGORIES, QUERY_TYPE_PERMISSION
from graph.auth_nodes.helpers import convert_permissions_to_tool_intents, build_bne_query_variables, get_auth_intent_parsing_system_prompt, get_client_permission_config, get_bne_query_template, call_bne_graphql_api, determine_user_authorization, get_client_auth_config
from graph.auth_nodes.authorization_classes import AuthorizationDetails, Result
from graph.state import State
from typing import List, Dict, Any
import httpx
import asyncio
import os
from graph.auth_nodes.context_vars import client_id_var
from graph.auth_nodes.cache import permissions_cache

async def analyze_user_intent_with_llm(client_id, user_msgs: list[str]):
    token = client_id_var.set(client_id)
    
    auth_intent = get_auth_intent_parsing_system_prompt(client_id)
    messages = [
    {
        "role": "system",
        "content": "Use these instructions to respond to the user:\n" + auth_intent
    },
]
    messages.append(
        {
            "role": "user",
            "content": f"These are the user's conversation:\n{user_msgs}\nFocus mostly on the most recent messages, the ones near the end of the list."
        }
    )
    llm_str = model.with_structured_output(AuthorizationDetails)
    resp = await llm_str.ainvoke(messages)  

    client_id_var.reset(token)
    
    return resp
        

async def query_bne_permissions(subcategory_name: str, auth_details: Dict[str, Any], user_info: Dict) -> Any:  
    uuid = user_info.get("uuid", None)

    if not uuid:
        raise ValueError("User info must contain a valid uuid.")
    permissions_map = get_client_permission_config(user_info.get("client_id", ""))
    
    query_type = permissions_map.get(subcategory_name, {}).get("bne_query", {}).get("query_type", None)
    
    bne_permission_code = permissions_map.get(subcategory_name, {}).get("bne_permissions", {}).get("permission_code", None)
    
    if query_type == QUERY_TYPE_PERMISSION and not bne_permission_code:
        raise ValueError(f"Permission code not found for subcategory: {subcategory_name}")
    
    query = get_bne_query_template(query_type)
    variables = build_bne_query_variables(query_type, ohid_uuid=uuid, groupId=auth_details.get("groupId", None), bne_permission_code=bne_permission_code)
    
    try:  
        response = await call_bne_graphql_api(query, variables)
        return await determine_user_authorization(subcategory_name, permissions_map, response)
    except httpx.RequestError as e:  
        print(f"Error fetching permissions: {e}")  
        return False
  
async def get_cached_or_fetch_permission(subcategory_name: str, auth_detail_dict: Dict[str, Any], user_info: dict) -> Any:  
    user_name = user_info.get("user_name", None)
    if not user_name:
        raise ValueError("User info must contain a valid user_name.")

    cache_key = (user_name, subcategory_name, tuple(sorted(auth_detail_dict.items())))  
    
    permission = await permissions_cache.get(cache_key)  
    if permission is not None:  
        return permission  
    
    permission = await query_bne_permissions(subcategory_name, auth_detail_dict, user_info)  
    print("Permission fetched from API:", permission)  
    
    await permissions_cache.set(cache_key, permission)  
    
    return permission  


def extract_auth_requirements_from_result(result: Result, user_info: dict) -> Dict[str, List[Dict[str, Any]]]:  
  
    auth_details = {}  
    permissions_map = get_client_permission_config(user_info.get("client_id", ""))
      
    for intent in result.intents:  
        if intent.category in GENERAL_NO_AUTH_INTENT_CATEGORIES:  
            continue  
          
        for subcategory in intent.subcategories:  
            if subcategory.is_auth_required:  
                if not subcategory.name or subcategory.name not in permissions_map:
                    raise ValueError(f"Invalid subcategory name: {subcategory.name}. It should be one of {list(permissions_map.keys())}")
                
                if subcategory.name not in auth_details:  
                    auth_details[subcategory.name] = []  
                detail_map = subcategory.details_map if subcategory.details_map else {}
                auth_details[subcategory.name].append(detail_map)  
      
    return auth_details 

  
async def verify_user_permissions(auth_details: Dict[str, List[Dict[str, Any]]], user_info: dict) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:  
    allowed_permissions = {}  
    denied_permissions = {}  
    
    permission_check_tasks = []
    for subcategory_name, details_list in auth_details.items():  
  
        for details in details_list:  
            task = asyncio.create_task(get_cached_or_fetch_permission(subcategory_name, details, user_info))
            permission_check_tasks.append((subcategory_name, details, task))
    
    for subcategory_name, details, task in permission_check_tasks:
        permission = await task
        if permission:  
            allowed_permissions[subcategory_name].append(details) if subcategory_name in allowed_permissions else allowed_permissions.setdefault(subcategory_name, []).append(details)
        else:  
            denied_permissions[subcategory_name].append(details) if subcategory_name in denied_permissions else denied_permissions.setdefault(subcategory_name, []).append(details)
  
    return {  
        'allowed_permissions': allowed_permissions,  
        'denied_permissions': denied_permissions  
    } 

async def perform_bne_authorization(state: State, auth_res: AuthorizationDetails):
    """Performs BnE authorization for the user's query. 
    """
    try:
        user_info = state.get("user_info", {})
        
        authorization_data = extract_auth_requirements_from_result(auth_res, user_info) 
        permissions_check = await verify_user_permissions(authorization_data, user_info)  
        
        allowed_permissions = permissions_check['allowed_permissions']  
        denied_permissions = permissions_check['denied_permissions']  
        
        all_denied = not allowed_permissions
        # messages = []  
    
        if all_denied:  
            return {"return_to_user": True, "messages": [AIMessage(content="Sorry, you are not authorized to perform this action. Please contact your administrator for further assistance.")]} 
        else:  
            # allowed_actions = "\n".join(  
            #     f"Action: {key.replace('_', ' ').title()}" + (f", Details: {', '.join(f'{detail_key}: {detail_value}' for detail_key, detail_value in details.items())}" if details else "")  
            #     for key in allowed_permissions  
            #     for details in allowed_permissions.get(key, [{}])  
            # ) 
            
            authorized_intents = convert_permissions_to_tool_intents(allowed_permissions, user_info, use_tool_names=True)                  
    
            denied_actions = "\n".join(  
                f"Action: {key.replace('_', ' ').title()}" + (f", Details: {', '.join(f'{detail_key}: {detail_value}' for detail_key, detail_value in details.items())}" if details else "")  
                for key in denied_permissions  
                for details in denied_permissions.get(key, [{}])  
            ) 

            if denied_actions:  
                # messages.append(f"Sorry, you are not authorized to perform following action/s: {denied_actions}. Please contact your administrator for further assistance.")  
                return {"return_to_user": True, "messages": [AIMessage(content=f"Sorry, you are not authorized to perform following action/s: {denied_actions}. Please contact your administrator for further assistance.")]} 
            # if allowed_actions:  
            #     messages.append(f"User is only authorized to perform the following actions: {allowed_actions}") 
    
            return {  
                "return_to_user": False,
                'is_auth_performed': True,
                'authorized_intents': authorized_intents,
            }  
            
    except Exception:
        raise Exception (traceback.format_exc())


async def authorize_and_route_to_workflow(state: State):
    """Consumes a user's query and determines whether or not the user requires authorization to access resources based on the query's intent.
    """
    try:
        client_id = state.get("user_info", {}).get("client_id", None)
        
        global_authorization_disabled = os.environ.get("DISABLE_AUTHORIZATION", "False").lower() == "true"
        client_authorization_disabled = get_client_auth_config(client_id).get("disable_auth")
        
        if global_authorization_disabled or client_authorization_disabled or state.get('is_auth_performed', None):
            print("-"*100)
            print("Skipping authorization check because either the AUTH is disabled or has already been completed at the parent level.")
            print("-"*100)
            return
        
        messages = state.get("messages")
        filtered_user_ai_messages = list(filter_messages(messages, include_types=[HumanMessage, AIMessage], exclude_names=["images"], exclude_tool_calls=True))
        message_types_and_content = [(type(message).__name__, message.content) for message in filtered_user_ai_messages] 
        
        client_id = state.get("user_info", {}).get("client_id", None)
        if not client_id:
            raise ValueError("Client ID is missing in the user_info state.")

        auth_res = await analyze_user_intent_with_llm(client_id, message_types_and_content)
        print("Authorization Intents: ", auth_res)
        if auth_res.intents is None or auth_res.error is not None:
            return {"return_to_user": True, "messages": [AIMessage(content=auth_res.error or "Sorry, I could not determine your intent. Please try rephrasing your query.")]}
    
        if not auth_res.is_auth_required:
            return {"return_to_user": False, 'is_auth_performed': True}
        
        if auth_res.is_data_required_from_user:
            return {"return_to_user": True, "messages": [AIMessage(content = auth_res.message)]}
        else:
            return await perform_bne_authorization(state, auth_res)

    except Exception:
        raise Exception (traceback.format_exc())