from __future__ import annotations
from typing import Any, Dict, List, Optional, Literal
from pydantic import BaseModel, Field, model_validator
from graph.auth_nodes.constants import GENERAL_NO_AUTH_INTENT_CATEGORIES
from dataclasses import dataclass  

class Subcategory(BaseModel):
    confidence: float = Field(
        ..., 
        description="Confidence score for this intent and details."
    )
    name: str = Field(
        ...,
        description="The name of the subcategory assigned to the user's query."
    )
    details_map: Optional[Dict[str, str]] = Field(  
        ...,
        description="A dictionary of required details for this operation in camel-casing and the values that have been provided by the user. This applies only if is_data_required_from_user is False." 
    )
    is_auth_required: bool = Field(..., description="Whether authorization is required for the assigned subcategory.")
    
class OperationIntent(BaseModel):
    category: Literal["member_management", "group_management", "plan_recommendation", "data_conversion", "surest_queries", "greeting", "cancellation", "miscellaneous"] = Field(
        ...,
        description="The intent category assigned to the user's query. Must be one of the allowed values."
    )
    subcategories: List[Subcategory] = Field(
        ...,
        description="A list of assigned subcategories under the category."
    )

    @model_validator(mode="after")
    def check_subcategories(self) -> "OperationIntent":
        allowed = {
            "member_management": [
                "view_member_details",
                "add_member",
                "update_member",
                "terminate_member"
            ],
            "group_management": [
                "view_group_details"
            ],
            "plan_recommendation": [
                "plan_recommendation"
            ],
            "data_conversion": [
                "convert_PDF_data_to_JSON"
            ],
            "surest_queries": [
                "surest_training_materials",
                "surest_qna",
                "view_surest_video",
                "surest_plan_comparison"
            ]
        }
        for subcategory in self.subcategories:
            if self.category not in GENERAL_NO_AUTH_INTENT_CATEGORIES and subcategory.name not in allowed[self.category]:
                raise ValueError(
                    f"Subcategory '{subcategory.name}' is not allowed for category '{self.category}'. Allowed: {allowed[self.category]}"
                )
        return self

class AuthorizationDetails(BaseModel):
    confidence: float = Field(..., description="Overall confidence score of the response.")

    intents: Optional[List[OperationIntent]] = Field(None, description="A list of parsed intents. This applies only if the response is valid.")
    
    is_auth_required: bool = Field(..., description="Whether authorization is required for any of the assigned subcategories.")
    
    is_data_required_from_user: bool = Field(..., description="Whether the user is required to provide any more data or if all required data has already been provided for every assigned subcategory.")
    
    message: Optional[str] = Field(None, description="A message that informs the user about the required data they need to provide and specifies which functionality uses that data. Mention only the missing fields that the user has not provided but are required. This applies only if is_data_required_from_user is True.")
    
    error: Optional[str] = Field(None, description="An error message if the user's intent cannot be determined.")
    
    warning: Optional[str] = Field(None, description="A warning message if some low-confidence intents were filtered out.")
    

@dataclass  
class Subcategory: 
    confidence: float 
    name: str  
    details_map: Optional[Dict[str, Any]]  
    is_auth_required: bool 
  
@dataclass  
class OperationIntent:    
    category: str  
    subcategories: List[Subcategory]  
  
@dataclass  
class Result:  
    confidence: float  
    intents: List[OperationIntent]  
    is_auth_required: bool  
    is_data_required_from_user: bool  
    message: str  
    error: Optional[str]  
    warning: Optional[str]
