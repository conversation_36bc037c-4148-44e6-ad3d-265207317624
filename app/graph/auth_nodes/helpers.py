import inspect  
import traceback
from functools import wraps 

auth_map = {
    "member_management": {
                            "subcategories": {"view_member_details": {"auth_required": False}, 
                                              "add_member": {"auth_required": False},
                                              "update_member": {"auth_required": False},
                                              "terminate_member": {"auth_required": False}}},
    "group_management": {"subcategories": {"view_group_details": {"auth_required": False}}},
    "plan_recommendation": {
                            "subcategories": {"plan_recommendation": {"auth_required": False}}},
    "data_conversion": {"subcategories": {"convert_PDF_data_to_JSON": {"auth_required": False}}},
    "surest_queries": {"subcategories": {"surest_training_materials": {"auth_required": False},
                        "surest_qna": {"auth_required": False},
                        "view_surest_video": {"auth_required": False},
                        "surest_plan_comparison": {"auth_required": False}}},
}

permissions_map = {
    "view_member_details": "VUEMEM",
    "add_member": "ADDMEM",
    "update_member": "UPDTMEM",
    "terminate_member": "TRMMEM",
}

reverse_permissions_map = {value: key for key, value in permissions_map.items()}

intent_to_tool_map = {
    "view_member_details": "fetch_member_details"
}

def convert_permissions_to_intents(input_data, use_tool_names=False):  
    intents = {}  
      
    for permission_code, entities in input_data.items():  
        intent = reverse_permissions_map.get(permission_code)  
        if use_tool_names and intent:  
            intent = intent_to_tool_map.get(intent, None) 
            if not intent:  
                raise ValueError(f"INVALID INTENT: '{intent}' not found in intent_to_tool_mapping")
        if intent:
            if intent not in intents:  
                intents[intent] = set()   
            for entity in entities:  
                intents[intent].update(entity.items())  
      
    return intents 


def validate_intent(state, func_name, entities):  
    
    auth_intents_map = state.get("authorized_intents", {})
    authorized_entities_set = auth_intents_map.get(func_name)  
    
    print(f"Validating intent for tool '{func_name}' with entities: {entities}, authorized entities: {authorized_entities_set}")
  
    if not authorized_entities_set:  
        return False  
    provided_entities_set = set(entities.items())  
    return provided_entities_set.issubset(authorized_entities_set) 
 

def requires_auth_validation(func):  
    @wraps(func)  
    async def wrapper(state, *args, **kwargs):  
        try:
            function_name = func.__name__  
            
            func_args = inspect.signature(func).bind(state, *args, **kwargs)  
            # func_args.apply_defaults()  
            all_arguments = func_args.arguments  
            groupId = all_arguments.get('memGroupID', None) or all_arguments.get('groupId', None)   
            if not groupId:  
                return f"Function '{function_name}' requires 'memGroupID' or 'groupId' argument to be provided."
            auth_entities = {  
                "groupId": groupId
            }  

            if not validate_intent(state, function_name, auth_entities):  
                return f"Intent '{function_name}' does not match for which current user was authorized."
            
            return await func(state, *args, **kwargs)
        
        except Exception as e:  
            traceback.print_exc() 
            raise Exception(traceback.format_exc()) 
    return wrapper