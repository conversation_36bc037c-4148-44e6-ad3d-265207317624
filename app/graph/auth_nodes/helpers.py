import inspect  
import traceback
from functools import wraps 
from graph.auth_nodes.constants import QUERY_TYPE_PERMISSION, QUERY_TYPE_PERSONA, PERSONA_ALL, AUTHORIZATION_INTENT_PARSING_SYSTEM_PROMPT, BNE_QUERY_MAP_COLLECTION, PERMISSION_MAPS_COLLECTION, AUTH_MAPS_COLLECTION
import httpx
from config.db import db_core
import json
import os
from asyncio import Lock
from typing import Tuple
import time

BNE_GRAPHQL_API_ENDPOINT = os.getenv("BNE_GRAPHQL_API_ENDPOINT")
BNE_OAUTH2_TOKEN_URL = os.getenv("BNE_OAUTH2_TOKEN_URL")
BNE_CLIENT_ID = os.getenv("BNE_CLIENT_ID")
BNE_CLIENT_SECRET = os.getenv("BNE_CLIENT_SECRET")
BNE_SCOPE= os.getenv("BNE_SCOPE")
BNE_ACCESS_TOKEN = None  
BNE_TOKEN_EXPIRY_TIMESTAMP = 0
bne_token_lock = Lock()

CLIENT_AUTH_MAPS = {}
CLIENT_PERMISSION_MAPS = {}
BNE_QUERY_TEMPLATES = {}

async def initialize_auth_maps():
    global CLIENT_AUTH_MAPS, CLIENT_PERMISSION_MAPS, BNE_QUERY_TEMPLATES
    CLIENT_AUTH_MAPS = await load_client_auth_maps_from_db()
    CLIENT_PERMISSION_MAPS = await load_client_permission_maps_from_db()
    BNE_QUERY_TEMPLATES = await load_bne_query_templates_from_db()
    
async def load_client_auth_maps_from_db():  
    cursor = db_core[AUTH_MAPS_COLLECTION].find()  
    auth_maps = {}  
    async for document in cursor:  
        client_id = document.get('client_id')  
        if client_id:  
            auth_maps[client_id] = {
            "disable_auth": document.get('disable_auth', False),
            "auth_map": document.get('auth_map') if 'auth_map' in document else None
            }
            if not auth_maps[client_id].get("auth_map"):
                raise ValueError(f"Auth map not found or invalid for client_id: {client_id}")
    return auth_maps  

async def load_client_permission_maps_from_db():  
    cursor = db_core[PERMISSION_MAPS_COLLECTION].find()  
    permission_maps = {}  
    async for document in cursor:  
        client_id = document.get('client_id')  
        if client_id:  
            permission_maps[client_id] =  document.get('permission_mappings') if 'permission_mappings' in document else None
            if not permission_maps[client_id]:
                raise ValueError(f"Auth map not found for client_id: {client_id}")
    return permission_maps  
  
async def load_bne_query_templates_from_db():  
    document = await db_core[BNE_QUERY_MAP_COLLECTION].find_one()  
    return document.get("queries") 

def get_auth_intent_parsing_system_prompt(client_id: str) -> str:
    auth_map = CLIENT_AUTH_MAPS.get(client_id, {}).get("auth_map", None)
    if not auth_map:
        raise ValueError(f"No authorization map found for client_id: {client_id}")

    auth_map_str = json.dumps(auth_map, indent=2)
    
    prompt = AUTHORIZATION_INTENT_PARSING_SYSTEM_PROMPT.format(AUTH_MAP=auth_map_str)
    return prompt

def get_client_permission_config(client_id: str) -> dict:
    permission_map = CLIENT_PERMISSION_MAPS.get(client_id, None)
    if not permission_map:
        raise ValueError(f"No permission map found for client_id: {client_id}")
    return permission_map

def get_client_auth_config(client_id: str) -> dict:
    auth_map = CLIENT_AUTH_MAPS.get(client_id, None)
    if not auth_map:
        raise ValueError(f"No auth map found for client_id: {client_id}")
    return auth_map

def get_bne_query_template(query_type: str) -> str:
    if not BNE_QUERY_TEMPLATES:
        raise ValueError("BNE query map is not initialized.")
    query = BNE_QUERY_TEMPLATES.get(query_type, None)
    if not query:
        raise ValueError(f"BNE query not found in the query map, the query type should be one of {BNE_QUERY_TEMPLATES.keys()}.")
    return query

def convert_permissions_to_tool_intents(input_data, user_info, use_tool_names=False):  
    intents = {}  
    permissions_map = get_client_permission_config(user_info.get("client_id", ""))
      
    for subcategory_name, entities in input_data.items():  
        intent = subcategory_name  
        if use_tool_names and intent:  
            intent = permissions_map.get(intent, None).get("tool", None)
            if not intent:  
                raise ValueError(f"INVALID tool for '{subcategory_name}' not found in PERMISSION_MAPPINGS.")
        if intent:
            if intent not in intents:  
                intents[intent] = set()   
            for entity in entities:  
                intents[intent].update(entity.items())  
      
    return intents 


def verify_user_tool_authorization(state, func_name, entities):  
    
    auth_intents_map = state.get("authorized_intents", {})
    authorized_entities_set = auth_intents_map.get(func_name)  
    
    print(f"Validating intent for tool '{func_name}' with entities: {entities}, authorized entities: {authorized_entities_set}")
  
    if not authorized_entities_set:  
        return False  
    provided_entities_set = set(entities.items())  
    return provided_entities_set.issubset(authorized_entities_set) 
 

def requires_auth_validation(func):  
    @wraps(func)  
    async def wrapper(state, *args, **kwargs):  
        try:
            function_name = func.__name__  
            func_args = inspect.signature(func).bind(state, *args, **kwargs)  
            # func_args.apply_defaults()  
            all_arguments = func_args.arguments  
            
            client_id = all_arguments.get('state', {}).get("user_info", {}).get("client_id", None)
            
            client_authorization_disabled = get_client_auth_config(client_id).get("disable_auth")
            
            if os.environ.get("DISABLE_AUTHORIZATION", "false").lower() == "true" or client_authorization_disabled:
                return await func(state, *args, **kwargs)
            
            groupId = all_arguments.get('memGroupID', None) or all_arguments.get('groupId', None)   
            if not groupId:  
                return f"Function '{function_name}' requires 'memGroupID' or 'groupId' argument to be provided."
            auth_entities = {  
                "groupId": groupId
            }  

            if not verify_user_tool_authorization(state, function_name, auth_entities):  
                return f"Intent '{function_name}' does not match for which current user was authorized."
            
            return await func(state, *args, **kwargs)
        
        except Exception as e:  
            traceback.print_exc() 
            raise Exception(traceback.format_exc()) 
    return wrapper


def build_bne_query_variables(query_type, **kwargs):

    if query_type == QUERY_TYPE_PERMISSION:
        if not all(param in kwargs for param in ["ohid_uuid", "groupId", "bne_permission_code"]):
            raise ValueError("Missing required parameters for permission map: ohid_uuid, groupId, bne_permission_code")
        
        group_id = kwargs.get("groupId")
        if not group_id:
            raise ValueError("Missing required parameter: groupId")
            
        return {
            "identifier": {
                "type": "OHID",
                "value": kwargs["ohid_uuid"]
            },
            "data": {
                "matchType": "ALL",
                "permissions": [kwargs.get("bne_permission_code")],
                "scopeId": kwargs.get("groupId"),
                "scopeType": "GROUP"
            }
        }
        
    elif query_type == QUERY_TYPE_PERSONA:
        if not all(param in kwargs for param in ["ohid_uuid"]):
            raise ValueError("Missing required parameter: ohid_uuid")
            
        return {
            "identifier": {
                "type": "OHID",
                "value": kwargs["ohid_uuid"]
            }
        }
    else:
        raise ValueError(f"Unknown map name: {query_type}")
    
    
def parse_user_profile_from_bne(response_data):
    if 'data' not in response_data or 'userProfile' not in response_data['data']:
        return None
    
    user_profile = response_data['data']['userProfile']
    
    personas = {persona.get("code", "").upper() for persona in user_profile.get('personas', [])}
    
    role_edges = user_profile.get('roles', {}).get('edges', [])
    roles = {edge['node']['code'] for edge in role_edges 
             if 'node' in edge and 'code' in edge['node']}
    
    return {
        'personas': personas,
        'roles': roles,
        'is_internal': 'INTERNAL' in personas
    }
    
    
def verify_access_by_role_and_persona(bne_user_info, permissions_map, subcategory_name):
 
    permission_config = permissions_map.get(subcategory_name, {})
    bne_permissions = permission_config.get("bne_permissions", {})
    allowed_persona = bne_permissions.get("allowed_persona", None)
    required_role_codes = set(bne_permissions.get("role_codes", []))
    
    print(f"Checking permission for subcategory '{subcategory_name}': "
            f"allowed_persona={allowed_persona}, required_role_codes={required_role_codes}, "
            f"user_personas={bne_user_info['personas']}, user_roles={bne_user_info['roles']}")
    
    # If all personas are allowed and no roles are required, grant permission  
    if allowed_persona == PERSONA_ALL and not required_role_codes:
        return True
    
    # If specific persona is required, check if user has it 
    if allowed_persona != PERSONA_ALL and allowed_persona not in bne_user_info['personas']:
        return False
    
    # If there are no role requirements, persona check suffices
    if not required_role_codes:
        return True
    
    # Grant permission if there is an intersection between required roles and user's roles  
    return bool(required_role_codes.intersection(bne_user_info['roles']))


async def determine_user_authorization(subcategory_name, permissions_map, response: httpx.Response) -> bool:
    response_json = response.json()
    
    if 'data' in response_json and 'checkUserPermissions' in response_json['data']:
        return response_json['data']['checkUserPermissions']['hasPermission']
    
    elif 'data' in response_json and 'userProfile' in response_json['data'] and response_json['data']['userProfile'] is not None:
        bne_user_info = parse_user_profile_from_bne(response_json)
        if not bne_user_info:
            return False
        return verify_access_by_role_and_persona(bne_user_info, permissions_map, subcategory_name)
    
    elif "errors" in response_json and response_json["errors"] is not None:
        raise ValueError(f"Error in response: {response_json['errors']}")
    
    return False

async def fetch_bne_oauth_token() -> Tuple[str, int]:  
    try:  
        async with httpx.AsyncClient() as client:
            response = await client.post(
                BNE_OAUTH2_TOKEN_URL, 
                data={  
                    'grant_type': 'client_credentials',  
                    'client_id': BNE_CLIENT_ID,  
                    'client_secret': BNE_CLIENT_SECRET,
                    'scope': BNE_SCOPE
                }
            )  
            response.raise_for_status()  
            token_data = response.json()  
            return token_data['access_token'], token_data['expires_in']  
    except httpx.RequestError as e:  
        print(f"Error fetching new token: {e}")  
        raise  
  
async def ensure_valid_bne_token():  
    global BNE_ACCESS_TOKEN, BNE_TOKEN_EXPIRY_TIMESTAMP
    
    async with bne_token_lock: 
        if time.time() >= BNE_TOKEN_EXPIRY_TIMESTAMP:  
            BNE_ACCESS_TOKEN, expires_in = await fetch_bne_oauth_token()  
            BNE_TOKEN_EXPIRY_TIMESTAMP = time.time() + expires_in 


async def call_bne_graphql_api(query, variables):
    global BNE_ACCESS_TOKEN
    
    await ensure_valid_bne_token() 
    
    headers = {  
        'Content-Type': 'application/json',  
        'Authorization': f'Bearer {BNE_ACCESS_TOKEN}',
        'x-upstream-env': 'stage',
        'User-Agent': 'lingo',  
        'Accept': '*/*',  
        'Accept-Encoding': 'gzip, deflate, br',  
        'Connection': 'keep-alive',
    }  
     
    async with httpx.AsyncClient() as client:
        response = await client.post(
            BNE_GRAPHQL_API_ENDPOINT, 
            json={'query': query, 'variables': variables}, 
            headers=headers
        ) 
        response.raise_for_status()
        return response
        