from langgraph.prebuilt import tools_condition
from langgraph.graph import E<PERSON>
from graph.state import State
from tools.primary_assistant_tools.router_tools import ToSamxOneAssistant, ToSurestAssistant, ToCirrusAssistant, ToPlanRecommendationAssistant, ToDataConverterAssistant
from graph.chains import primary_assistant_tools
from utils.general_utils import validate_tool_call
from typing import Literal
from tools.common_tools import CompleteOrEscalate
from services.event_logger_service import logger
from tools.primary_assistant_tools.router_tools import router_tools
from utils.helpers.constants import OEC_DEFAULT_CLIENT_ID, OEC_SUREST_CLIENT_ID, OEC_SAMXONE_CLIENT_ID

async def route_primary_assistant(
    state: State,
):
    route = tools_condition(state)
    if route == END:
        return END
    if route == "tools" and validate_tool_call(state["messages"][-1].tool_calls, primary_assistant_tools):
        #INFO: Log agent traversal
        tool_calls = state["messages"][-1].tool_calls
        if len(tool_calls) > 1:
            return "restrict_parallel_agents_run"
        if tool_calls[0]["name"] in router_tools:
            uuid = state.get("user_info")["uuid"]
            user_name = state.get("user_info")["user_name"]
            session_id = state.get("user_info")["session_id"]
            client_id = state.get("user_info")["client_id"]
            await logger.info(uuid, user_name, session_id, client_id, "router", "route_primary_assistant", "State", tool_calls[0]["name"], None, None)
        if tool_calls[0]["name"] == ToPlanRecommendationAssistant.__name__:
            return "enter_plan_recommendation_assistant"
        if tool_calls[0]["name"] == ToCirrusAssistant.__name__ :
            return "enter_cirrus_assistant"
        elif tool_calls[0]["name"] == ToDataConverterAssistant.__name__:
            return "enter_data_converter_assistant"
        elif tool_calls[0]["name"] == ToSurestAssistant.__name__ :
            if state["user_info"]["client_id"] in [OEC_DEFAULT_CLIENT_ID, OEC_SUREST_CLIENT_ID]:
                return "enter_surest_assistant"
            else:
                return "permission_check"
        elif tool_calls[0]["name"] == ToSamxOneAssistant.__name__ :
            if state["user_info"]["client_id"] == OEC_SAMXONE_CLIENT_ID:
                return "enter_samxone_assistant"
            else:
                return "permission_check"
        else:
            return "call_primary_tool"
        
    elif route == "tools":
        return "invalid_tool"
    
    return "primary_assistant"


def route_plan_recommendation_assistant(
    state: State,
):
    route = tools_condition(state)
    if route == END:
        return END
    if route == "tools":
        tool_calls = state["messages"][-1].tool_calls
        if len(tool_calls) > 1 and CompleteOrEscalate.__name__ in [call["name"] for call in tool_calls]:
            return "escalation_fallback"
        if tool_calls[0]["name"] == CompleteOrEscalate.__name__:
            return "leave_skill"
        return "call_plan_recommendation_tool"
    return "plan_recommendation_assistant"
    
    
def route_cirrus_assistant(
    state: State,
):
    route = tools_condition(state)
    if route == END:
        return END
    if route == "tools":
        tool_calls = state["messages"][-1].tool_calls
        if len(tool_calls) > 1 and CompleteOrEscalate.__name__ in [call["name"] for call in tool_calls]:
            return "escalation_fallback"
        if tool_calls[0]["name"] == 'check_member_status':
            return "call_cirrus_tool_with_assistant"
        elif tool_calls[0]["name"] == CompleteOrEscalate.__name__:
            return "leave_skill"
        else:
            return "call_cirrus_tool"
    return "cirrus_assistant"
        
        
def route_data_converter_assistant(
    state: State,
):
    route = tools_condition(state)
    if route == END:
        return END
    if route == "tools":
        tool_calls = state["messages"][-1].tool_calls
        if len(tool_calls) > 1 and CompleteOrEscalate.__name__ in [call["name"] for call in tool_calls]:
            return "escalation_fallback"
        if tool_calls[0]["name"] == CompleteOrEscalate.__name__:
            return "leave_skill"
        return "call_data_converter_tool"
    return "data_converter_assistant"


async def route_to_workflow(
    state: State,
) -> Literal[
    "primary_assistant",
    "cirrus_assistant",
    "plan_recommendation_assistant",
    "data_converter_assistant",
    "surest_assistant",
    "samxone_assistant"
]:
    """If we are in a delegated state, route directly to the appropriate assistant."""
    dialog_state = state.get("dialog_state")
    #INFO: Log agent traversal
    uuid = state.get("user_info")["uuid"]
    user_name = state.get("user_info")["user_name"]
    session_id = state.get("user_info")["session_id"]
    client_id = state.get("user_info")["client_id"]
    await logger.info(uuid, user_name,session_id, client_id, "router", "route_to_workflow", "State", dialog_state[-1] if dialog_state else "primary_assistant", None, None)
    if not dialog_state:
        return "primary_assistant"
    return dialog_state[-1]

# Each of the literals listed below must have a corresponding assistant and corresponding edges in the nodes_and_edges_registry
def route_escalation_fallback(
    state: State,
) -> Literal[
    "primary_assistant",
    "cirrus_assistant",
    "plan_recommendation_assistant",
    "data_converter_assistant",
    "surest_assistant",
    "samxone_assistant",
    "portal_form_enrollment_assistant",
    "nonauth_documents_search_assistant"
]:
    """If we are in a delegated state, route directly to the appropriate assistant."""
    dialog_state = state.get("dialog_state")
    if not dialog_state:
        return "primary_assistant"
    return dialog_state[-1]
