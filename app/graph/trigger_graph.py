from utils.tool_utils import _print_event
from utils.agent_utils import clear_and_summarize_messages, clear_pdf_data
from services.event_logger_service import logger
from utils.graph_utils import delete_dummy_thread
# from config.langfuse import get_langfuse_handler
_printed = set()
from utils.helpers.constants import OEC_DEFAULT_CLIENT_ID, OEC_SUREST_CLIENT_ID, OEC_SAMXONE_CLIENT_ID, OEC_BNEPORTAL_CLIENT_ID, OEC_BNEMOBILE_CLIENT_ID, OEC_OPTUMAI_CLIENT_ID, OEC_GCOREPORTING_CLIENT_ID
from utils.subgraph_registry import SubgraphRegistry
import os

async def route_compiled_graph(client_id: str):
    if client_id == OEC_DEFAULT_CLIENT_ID:
        return SubgraphRegistry.get("supervisor"), True 
    elif client_id == OEC_SUREST_CLIENT_ID or client_id == OEC_OPTUMAI_CLIENT_ID:
        return SubgraphRegistry.get("surest"), False
    elif client_id == OEC_SAMXONE_CLIENT_ID:
        return SubgraphRegistry.get("samxone_multiagent"), True
    elif client_id == OEC_BNEPORTAL_CLIENT_ID:
        if os.getenv('ENV').lower()=='dev':
           return SubgraphRegistry.get("bneportal"), True
        else:
            return SubgraphRegistry.get("nonauth_documents_search"), False
    elif client_id == OEC_BNEMOBILE_CLIENT_ID:
        return SubgraphRegistry.get("bnemobile"), True
    elif client_id == OEC_GCOREPORTING_CLIENT_ID:
        return SubgraphRegistry.get("gco_reporting"), False

user_permissions = {}


async def run_assistant(user_input: str, uuid: str, user_name: str, session_id: str, client_id: str, review_payload: dict, encoded_pdf: str=None, additional_arg: dict=None):
    config = {
    "configurable": {
        "thread_id": session_id,
        },
    # "callbacks": [get_langfuse_handler(client_id)],
    "metadata": {
            "langfuse_session_id": session_id,
            "langfuse_user_id": client_id
        }
    }
    print("UUID:", uuid, "User:", user_name, "Session_ID:", session_id, "Client_ID:", client_id)
    

    user_info = {"client_id": client_id, "uuid": uuid, "user_name": user_name, "session_id": session_id}
    assistant_graph, is_multiagent = await route_compiled_graph(client_id)

    input_data = {"messages": user_input, "user_info": user_info, "review_payload": review_payload, "user_permissions": user_permissions, "is_multiagent": is_multiagent, "is_auth_performed": False, "authorized_intents": {}, "additional_arg": additional_arg or {}}
        
    encoded_pdf and input_data.update({"pdf_data": encoded_pdf})
    
    async for event in assistant_graph.astream(input_data, config, stream_mode="values"):
       resp = _print_event(event, _printed)
       
    await clear_pdf_data(assistant_graph, config)
    await clear_and_summarize_messages(assistant_graph, config)
    
    await delete_dummy_thread(session_id, client_id)
    
    #INFO: Returning the response
    await logger.info(uuid, user_name, session_id, client_id, "function", "run_assistant", {"messages": user_input if not encoded_pdf else "pdf_data", "user_info": user_info, "review_payload": review_payload, "additional_arg": additional_arg}, resp, 200, None)


    return resp
