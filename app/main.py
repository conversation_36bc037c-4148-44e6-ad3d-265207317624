from dotenv import load_dotenv
load_dotenv()

from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from routes import api, cirrus_api, member_enroll_api, bne_api
from fastapi.exceptions import RequestValidationError  
from fastapi.responses import JSONResponse  
from starlette.middleware.base import BaseHTTPMiddleware
from utils.api_security_utils import authorize_request
from services.event_logger_service import logger
from contextlib import asynccontextmanager
from config.langfuse import httpx_client, initialize_handler
from config.postgres import init_auto_commit_pool, init_connection_pool, close_connection_pools
from graph.state import get_postgres_checkpointer
from graph.graph import init_graph
from subgraph.initialize_subgraphs import initialize_subgraphs_on_startup

EXCLUDED_ENDPOINTS = ["/", "/health", "/userAuth", "/OhidLogin", "/generateLingoToken", "/agenticOfflineEvaluation", "/fetchAllowedOrigin", "/docs", "/openapi.json", "/refreshLingoToken","/docExtract","/docExtractionCallback","/bne-training-material","/getEnvVariables", "/generateResponseStargate"]

class AuthorizationMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        if request.url.path not in EXCLUDED_ENDPOINTS:
            try:
                authorization = request.headers.get("Authorization")
                if not authorization:
                    raise HTTPException(status_code=401, detail="Unauthorized: Missing Authorization header")
                client_type = request.headers.get("Client-Type")
                await authorize_request(authorization=authorization, request=request,client_type=client_type)
            except HTTPException as e:
                return JSONResponse(status_code=e.status_code, content={"status": "error", "message": e.detail})

        response = await call_next(request)
        return response

@asynccontextmanager
async def lifespan(app: FastAPI):
    await initialize_handler()
    await init_auto_commit_pool()
    await init_connection_pool()
    await get_postgres_checkpointer()
    await initialize_subgraphs_on_startup()
    await init_graph() #INITIALIZATION OF THE LEGACY GRAPH, NOT IN USE
    yield
    httpx_client.close()
    await close_connection_pools()


app = FastAPI(title="LingoOEC FastAPI", version="0.1.0",lifespan=lifespan)
        

@app.exception_handler(RequestValidationError)   
async def validation_exception_handler(request: Request, exc: RequestValidationError):  
    try:
        req = await request.json()
        uuid = req.get("uuid", None)
        msid = uuid.split('-')[0] if uuid else None
        client_id = req.get("client_id", None)
        await logger.error(uuid, msid, client_id, "api", "validation_error", req, None, 422, str(exc.errors()), str(request.url))
    except Exception as e:
        try:
            form = await request.form()
            req = {field: value for field, value in form.items()}  
            uuid = req.get("uuid", None)
            msid = uuid.split('-')[0] if uuid else None
            client_id = req.get("client_id", None)
            await logger.error(uuid, msid, client_id, "api", "validation_error", req, None, 422, str(exc.errors()), str(request.url))
        except Exception as e:
            await logger.error(None, None, None, "api", "validation_error", None, None, 422, str(exc.errors()), str(request.url))
    finally:
        return JSONResponse(          
        status_code=422,          
        content={    
                "status": "error",   
                "response": exc.errors(),                      
            }  
        )

app.add_middleware(AuthorizationMiddleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api.router)
app.include_router(bne_api.router)
app.include_router(cirrus_api.router)
app.include_router(member_enroll_api.router)

@app.get("/")
async def root():
    return {"message": "LingoOEC FastAPI is Up & Running"}

@app.get("/health")
async def health():
    return {"200 OK"}
