import httpx  
import json  
import asyncio
  
async def llama():  
    # Define the authentication URL and credentials.  
    # Use the following URL for production server  
    auth = "https://api.uhg.com/oauth2/token"  
      
    client_id = "4ced791b-d036-4783-9dd0-bba7fe759c10"
    client_secret = "K5fN*Iv6He#wOjR9tUdlrvZf8jJp2uRU3Vu$"
    scope = "https://api.uhg.com/.default"    
    grant_type = "client_credentials"    
  
    async with httpx.AsyncClient() as client:    
        body = {    
            "grant_type": grant_type,    
            "scope": scope,    
            "client_id": client_id,    
            "client_secret": client_secret,    
        }    
        headers = {"Content-Type": "application/x-www-form-urlencoded"}    
        resp = await client.post(auth, headers=headers, data=body, timeout=60)    
        access_token = resp.json()["access_token"]  
  
        deployment_name = "meta-llama-3-1_8b-instruct"  
        uais_project_id = "33de7400-191f-4e76-9e11-82a12254eced"    
        url = "https://api.uhg.com/api/cloud/api-management/ai-gateway/1.0"  
  
        headers = {  
            "Authorization": f"Bearer {access_token}",  
            "deployment-id": deployment_name,  
            "projectId": uais_project_id,  
            "Content-Type": "application/json"  
        }  
  
        messages = [  
            {"role": "system", "content": "You are a helpful assistant. Keep answers to less than 20 words"},  
            {"role": "user", "content": "who are u?"}  
        ]  
  
        data = {  
            "messages": messages,  
            "max_tokens": 30  
        }  
  
        response = await client.post(f"{url}/chat/completions", headers=headers, data=json.dumps(data))  
        print(response.json())  
  
# Call the function  
if __name__ == "__main__":
    asyncio.run(llama())