import httpx  
import json  
import asyncio
import os
import logging
from langchain.llms.base import BaseLL<PERSON>
from typing import Any, List, Mapping, Optional, Dict
from langchain_core.callbacks import CallbackManagerForLLMRun
from langchain_core.outputs import Generation, LLMResult

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import OpenAI for fallback
import importlib.util
openai_spec = importlib.util.find_spec('config.openAI')
openai_module = None
if openai_spec:
    openai_module = importlib.util.module_from_spec(openai_spec)
    openai_spec.loader.exec_module(openai_module)
    logger.info("Successfully imported OpenAI module for fallback")

class LlamaModel(BaseLLM):
    """LangChain compatible model for Llama API access"""
    
    deployment_name: str = "meta-llama-3-1_8b-instruct"
    temperature: float = 0
    use_fallback: bool = True
    
    def _llm_type(self) -> str:
        return "llama_api"
    
    async def _agenerate(
        self, prompts: List[str], stop: Optional[List[str]] = None, run_manager: Optional[CallbackManagerForLLMRun] = None, **kwargs
    ) -> LLMResult:
        """Generate method that handles multiple prompts"""
        generations = []
        for prompt in prompts:
            messages = [{"role": "user", "content": prompt}]
            try:
                result = await llama(messages, max_tokens=kwargs.get("max_tokens", 1000))
                text = result["choices"][0]["message"]["content"]
                generations.append([Generation(text=text)])
            except Exception as e:
                logger.error(f"Error in Llama API call: {str(e)}")
                if self.use_fallback and openai_module:
                    logger.info("🔄 Falling back to OpenAI")
                    openai_result = await self._fallback_to_openai(prompt, **kwargs)
                    generations.append([Generation(text=openai_result)])
                else:
                    raise
        return LLMResult(generations=generations)
    
    async def _fallback_to_openai(self, prompt: str, **kwargs) -> str:
        """Fallback to OpenAI when Llama API fails"""
        if not openai_module:
            raise ValueError("OpenAI module not available for fallback")
            
        try:
            # Get the OpenAI model
            openai_model = openai_module.model
            # Process with OpenAI
            result = await openai_model._acall(prompt, **kwargs)
            return result
        except Exception as e:
            logger.error(f"OpenAI fallback also failed: {str(e)}")
            return "I apologize, but I'm currently experiencing technical difficulties. Please try again later."
    
    def _generate(
        self, prompts: List[str], stop: Optional[List[str]] = None, run_manager: Optional[CallbackManagerForLLMRun] = None, **kwargs
    ) -> LLMResult:
        """Required implementation of _generate for BaseLLM"""
        return asyncio.run(self._agenerate(prompts, stop=stop, run_manager=run_manager, **kwargs))
    
    async def _acall(self, prompt: str, stop: Optional[List[str]] = None, **kwargs) -> Dict[str, Any]:
        try:
            messages = [{"role": "user", "content": prompt}]
            result = await llama(messages, max_tokens=kwargs.get("max_tokens", 1000))
            return {"generations": [{"text": result["choices"][0]["message"]["content"]}]}
        except Exception as e:
            logger.error(f"Error in Llama API call: {str(e)}")
            if self.use_fallback and openai_module:
                logger.info("🔄 Falling back to OpenAI")
                openai_result = await self._fallback_to_openai(prompt, **kwargs)
                return {"generations": [{"text": openai_result}]}
            else:
                raise
    
    def _call(self, prompt: str, stop: Optional[List[str]] = None, **kwargs) -> str:
        """Call the Llama API and return the response"""
        result = asyncio.run(self._acall(prompt, stop=stop, **kwargs))
        return result["generations"][0]["text"]
    
    def bind_tools(self, tools, **kwargs):
        """Bind tools to the model for function calling capabilities"""
        from langchain.agents.format_scratchpad import format_to_openai_function_messages
        from langchain.agents.output_parsers import OpenAIFunctionsAgentOutputParser
        from langchain_core.runnables import RunnablePassthrough, RunnableLambda
        from langchain_core.messages import HumanMessage, AIMessage
        
        # If we're configured to use fallback and OpenAI is imported, prefer to use OpenAI directly
        # for tool binding since it's more reliable
        if self.use_fallback and openai_module:
            logger.info("🔄 Using OpenAI for tool binding since fallback is enabled")
            try:
                return openai_module.model.bind_tools(tools, **kwargs)
            except Exception as e:
                logger.error(f"Failed to use OpenAI for tool binding: {str(e)}. Falling back to Llama implementation.")
        
        # Create a copy of the tools that can be used with the model
        functions = [convert_to_openai_function(tool) for tool in tools]
        
        # Define a robust input handler for messages
        def _robust_input_handler(inputs):
            logger.info("🦙 Processing input in bind_tools")
            # Ensure we're starting with a dictionary
            if not isinstance(inputs, dict):
                logger.warning(f"🦙 Input not a dict, got: {type(inputs)}")
                # Convert non-dict inputs to a standard dict format
                if hasattr(inputs, "content"):
                    # Message object
                    return {"messages": [inputs], "agent_scratchpad": []}
                elif isinstance(inputs, list):
                    # List of messages
                    return {"messages": inputs, "agent_scratchpad": []}
                else:
                    # String or other type
                    return {"messages": [HumanMessage(content=str(inputs))], "agent_scratchpad": []}
            
            # Make sure the dict has the expected keys
            if "messages" not in inputs:
                logger.warning("🦙 Input dict missing 'messages' key")
                # Try to extract from other common keys
                if "input" in inputs:
                    inputs["messages"] = inputs["input"]
                elif "query" in inputs:
                    inputs["messages"] = inputs["query"]
                else:
                    # Create empty messages list as fallback
                    inputs["messages"] = []
            
            # Ensure agent_scratchpad exists
            if "agent_scratchpad" not in inputs:
                inputs["agent_scratchpad"] = []
                
            return inputs
        
        # Format messages correctly for the model
        def _format_messages(inputs):
            # Now we can safely assume inputs is a dict with required keys
            messages = inputs.get("messages", [])
            agent_scratchpad = inputs.get("agent_scratchpad", [])
            
            # Ensure messages is a list
            if not isinstance(messages, list):
                messages = [messages]
                
            # Convert string messages to HumanMessage objects
            messages = [
                HumanMessage(content=msg) if isinstance(msg, str) else msg
                for msg in messages
            ]
            
            tool_messages = format_to_openai_function_messages(agent_scratchpad)
            return messages + tool_messages
        
        # Ensure output is properly formatted for the agent
        def _ensure_dict_output(outputs):
            if isinstance(outputs, dict):
                return outputs
            
            # Handle AIMessage or other message types
            if hasattr(outputs, "content"):
                return {"messages": outputs}
            
            # Handle strings or other primitive types
            return {"messages": AIMessage(content=str(outputs))}
        
        # Create a fully wrapped chain that guarantees dict inputs/outputs
        input_chain = RunnableLambda(_robust_input_handler)
        process_chain = RunnablePassthrough.assign(
            agent_outcome=RunnableLambda(lambda x: _format_messages(x)) | self | RunnableLambda(_ensure_dict_output) | OpenAIFunctionsAgentOutputParser()
        )
        
        return input_chain | process_chain

# Helper function to convert LangChain tools to OpenAI function format
def convert_to_openai_function(tool):
    """Convert a LangChain tool to OpenAI function format"""
    import inspect
    
    # Handle tools created with @tool decorator
    if hasattr(tool, 'function'):
        name = getattr(tool, '__name__', tool.function.__name__)
        description = getattr(tool, 'description', tool.__doc__ or '')
        
        # Get parameters from function signature
        sig = inspect.signature(tool.function)
        parameters = {}
        required = []
        
        for param_name, param in sig.parameters.items():
            # Skip first parameter if it's 'state' (used by langgraph's InjectedState)
            if param_name == 'state':
                continue
                
            parameters[param_name] = {
                "type": "string"  # Default to string type
            }
            
            # Add to required if no default value
            if param.default == inspect.Parameter.empty:
                required.append(param_name)
        
        return {
            "name": name,
            "description": description,
            "parameters": {
                "type": "object",
                "properties": parameters,
                "required": required
            }
        }
    
    # Handle traditional LangChain tools with schema
    elif hasattr(tool, 'name') and hasattr(tool, 'description'):
        # This is for standard LangChain tools
        schema = tool.args_schema.schema() if hasattr(tool, "args_schema") else {"properties": {}, "required": []}
        return {
            "name": tool.name,
            "description": tool.description,
            "parameters": {
                "type": "object",
                "properties": schema.get("properties", {}),
                "required": schema.get("required", []),
            },
        }
    
    # Fallback for other tool types
    else:
        # Try to extract name and description any way we can
        name = getattr(tool, '__name__', getattr(tool, 'name', str(tool)))
        description = getattr(tool, '__doc__', getattr(tool, 'description', "Tool without description"))
        
        return {
            "name": name,
            "description": description,
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }

async def llama(messages, max_tokens=1000):
    """
    Connect to the Llama API and get a response.
    
    Args:
        messages (list): List of message objects to send to the Llama API.
        max_tokens (int): Maximum number of tokens to generate in the response.
    
    Returns:
        dict: The JSON response from the Llama API.
    """
    # Add visible logging to show when Llama is being used
    logger.info("🦙 LLAMA MODEL API CALL 🦙")
    logger.info(f"🦙 Messages: {messages[:1]} (truncated)")  # Only show first message to avoid long logs
    
    # Define the authentication URL and credentials
    auth = "https://api.uhg.com/oauth2/token"
    client_id = os.environ.get("OPENAI_CLIENT_ID", "4ced791b-d036-4783-9dd0-bba7fe759c10")
    client_secret = os.environ.get("OPENAI_CLIENT_SECRET", "K5fN*Iv6He#wOjR9tUdlrvZf8jJp2uRU3Vu$")
    scope = "https://api.uhg.com/.default"
    grant_type = "client_credentials"

    try:
        async with httpx.AsyncClient() as client:
            # Step 1: Get access token
            logger.info("Requesting OAuth2 token from %s", auth)
            body = {
                "grant_type": grant_type,
                "scope": scope,
                "client_id": client_id,
                "client_secret": client_secret,
            }
            headers = {"Content-Type": "application/x-www-form-urlencoded"}
            try:
                resp = await client.post(auth, headers=headers, data=body, timeout=10)
            except httpx.TimeoutException:
                logger.error(f"Timeout while requesting OAuth2 token from {auth}")
                raise Exception(f"Timeout while requesting OAuth2 token from {auth}")

            if resp.status_code != 200:
                logger.error(f"Authentication failed: {resp.status_code} - {resp.text}")
                raise Exception(f"Authentication failed: {resp.text}")

            access_token = resp.json()["access_token"]

            # Step 2: Call Llama API
            logger.info("Requesting Llama API at %s", url)
            deployment_name = "meta-llama-3-1_8b-instruct"
            uais_project_id = os.environ.get("OPENAI_PROJECT_ID", "33de7400-191f-4e76-9e11-82a12254eced")
            url = "https://api.uhg.com/api/cloud/api-management/ai-gateway/1.0"

            headers = {
                "Authorization": f"Bearer {access_token}",
                "deployment-id": deployment_name,
                "projectId": uais_project_id,
                "Content-Type": "application/json"
            }

            # Add a system message to indicate Llama is being used (only if one doesn't exist already)
            has_system_message = any(msg.get("role") == "system" for msg in messages)
            if not has_system_message:
                messages.insert(0, {"role": "system", "content": "You are a helpful Surest assistant powered by Llama 3. When providing information, be specific and clear."})
            else:
                # Modify existing system message to indicate Llama
                for msg in messages:
                    if msg.get("role") == "system":
                        if "powered by Llama" not in msg["content"]:
                            msg["content"] = msg["content"] + " (Powered by Llama 3)"
                        break

            data = {
                "messages": messages,
                "max_tokens": max_tokens
            }

            try:
                response = await client.post(f"{url}/chat/completions", headers=headers, data=json.dumps(data), timeout=10)
            except httpx.TimeoutException:
                logger.error(f"Timeout while requesting Llama API at {url}/chat/completions")
                raise Exception(f"Timeout while requesting Llama API at {url}/chat/completions")

            if response.status_code != 200:
                logger.error(f"API request failed: {response.status_code} - {response.text}")
                raise Exception(f"API request failed: {response.text}")

            # Add a model indicator to the response
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                if "message" in result["choices"][0]:
                    content = result["choices"][0]["message"]["content"]
                    # Add a subtle indicator to identify it came from Llama but don't interfere with response
                    if "Tool Calls:" not in content and "function_call" not in str(result["choices"][0]):
                        pass  # Don't modify tool call responses
            
            return result
    except Exception as e:
        logger.error(f"Error calling Llama API: {str(e)}")
        raise

def get_llama_model():
    """Create and return a Llama model instance for use with LangChain"""
    logger.info("🦙 Creating Llama model for Surest assistant with fallback enabled 🦙")
    return LlamaModel(deployment_name="meta-llama-3-1_8b-instruct", temperature=0, use_fallback=True)

def model():
    """Exported model instance for import elsewhere (for compatibility with 'from config.llamaAI import model')."""
    return get_llama_model()

# For backward compatibility with code expecting 'model' as an object, not a function
model = get_llama_model()

# If the file is run directly, execute a simple test
if __name__ == "__main__":
    async def test_llama():
        messages = [  
            {"role": "system", "content": "You are a helpful assistant. Keep answers to less than 20 words"},  
            {"role": "user", "content": "Give me sample Java code?"}  
        ]  
        try:
            result = await llama(messages)
            print(result)
        except Exception as e:
            print(f"Llama API failed: {e}")
            if openai_module:
                print("Testing fallback to OpenAI")
                model = get_llama_model()
                result = await model._fallback_to_openai("Give me sample Java code")
                print(f"OpenAI fallback result: {result}")
    
    asyncio.run(test_llama())
