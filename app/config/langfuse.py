import os  
import httpx  
from langfuse.callback import Callback<PERSON><PERSON><PERSON>  
from config.db import db_onboarding
from dotenv import load_dotenv
load_dotenv()
 
current_env = os.getenv("ENV")
collection=db_onboarding["Configs"]
httpx_client = httpx.Client(verify=os.getenv("VERIFY_SSL") == "True")  
handlers = {}  

async def initialize_handler():  
    langfuse_config_object = await collection.find_one({"environment": current_env.lower()}, {"_id": 0, "environment": 0})
    if not langfuse_config_object:  
        raise Exception(f"No configuration found for environment '{current_env}'") 

    for client_type in langfuse_config_object:  
        secret_key = langfuse_config_object[client_type]["secret_key"]  
        public_key = langfuse_config_object[client_type]["public_key"]  
        handler = CallbackHandler(secret_key=secret_key, public_key=public_key, httpx_client=httpx_client)  
        handlers[client_type] = handler 

def get_langfuse_handler(client_id: str):  
    client_id_lower = client_id.lower()  
    if client_id_lower in handlers:  
        return handlers[client_id_lower]  
    else:  
        raise Exception(f"Invalid client {client_id}") 
  

