import os
from pymongo import MongoClient
from motor.motor_asyncio import AsyncIOMotorClient

db_url = os.getenv("MONGO_DB_URL")
db_name_start = os.getenv("DB_START_STRING")
db_name_member_enroll = os.getenv("DB_MEMBER_ENROLL")

env = os.getenv("ENV")
internal_db = db_name_start + "_Internal_" + env
onboarding_db = db_name_start + "_Onboarding"

client = AsyncIOMotorClient(db_url)
prompts_client = MongoClient(db_url)

db_core = client[os.getenv("DB_CORE")]
prompts_db_core = prompts_client[os.getenv("DB_CORE")]
db = client[internal_db]
db_member_enroll = client[db_name_member_enroll]
db_onboarding = client[onboarding_db]
sync_db_onboarding = prompts_client[onboarding_db]

samx_one_db_url = os.getenv("SAMX_ONE_DB_URL")
samx_one_db_name = os.getenv("SAMX_ONE_DB_NAME")
samx_one_client = AsyncIOMotorClient(samx_one_db_url)
samx_one_db = samx_one_client[samx_one_db_name]

async def get_database(client_id: str):
    if client_id == "Internal": 
        return internal_db
    else:
        return db_name_start + "_" + client_id
