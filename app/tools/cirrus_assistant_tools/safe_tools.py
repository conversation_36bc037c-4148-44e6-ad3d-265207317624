from langchain_core.tools import tool
from services.cirrus import get_member_group_service, member_search_service, get_contract_options_service
from services import member_status_service
from utils.schemas.cirrus_classes import CirrusMemberSearch
from pydantic import Field
import traceback
from langgraph.prebuilt.tool_node import InjectedState
from typing import Annotated, Optional
from utils.general_utils import convert_date_format
from utils.tool_utils import log_tool_error
from graph.auth_nodes.helpers import requires_auth_validation

@tool
async def fetch_group_contract_details(state: Annotated[dict, InjectedState], memberGroupID: str = Field(
                        description="A 7 or 8 digit numeric string representing the member group ID."
                        ),
                        onlyContract: bool = Field(
                        description="A boolean value to indicate if the plan details are to be fetched or not."
                        ),
                        allGroupInfo: bool = Field(
                        description="A boolean value to indicate if all the group details and plan details are required."
                        )
                    ):
    """Fetches or searches group details or member group details or plan/contract details of a member group in cirrus.
    onlyContract is set if and only if user asks for plan/contract option details specifically
    
    Args:
        memberGroupID: A 7 or 8 digit numeric string representing the member group ID.
    """
    try:
        user = state.get("user_info")
        clientId = user.get("client_id")
        uuid = user.get("uuid")
        user_name = state.get("user_info")["user_name"]
        session_id = state.get("user_info")["session_id"]
        if onlyContract:
            response = await get_contract_options_service.get_contract_options(memberGroupID, clientId, session_id, user_name)
            if not isinstance(response, dict):
                return response            
            product_plan_info = response.get('productPlanInfoList', None)
            if not product_plan_info:
                return f"No product plan information found for the given member group ID: {memberGroupID}"
            return product_plan_info
        elif allGroupInfo:
            response = {}
            response = await get_member_group_service.get_member_group(memberGroupID, clientId, session_id, user_name)
            if not isinstance(response, dict):
                return response
            contract_options = await get_contract_options_service.get_contract_options(memberGroupID, clientId, session_id, user_name)
            if not isinstance(contract_options, dict):
                return contract_options
            response["products&PlanInfo"] = contract_options.get('productPlanInfoList', None)
            insuring_rules_info = contract_options.get('insuringRuleInfo', None)
            if isinstance(insuring_rules_info,dict):
                response['groupInformation']['waitingPeriodDurationType'] =insuring_rules_info.get('waitingPeriodDurationType', None)
                response['groupInformation']['waitingPeriodCount'] = insuring_rules_info.get('waitingPeriodCount', None)
                response['groupInformation']['waitingPeriodType'] = insuring_rules_info.get('waitingPeriodType', None)
            return response
        else:
            return f"Your query was not clear please try reframing it for Member group ID: {memberGroupID}"
    except:
        await log_tool_error(state, traceback.format_exc(), "fetch_group_contract_details")
        raise Exception (traceback.format_exc())
    

@tool
@requires_auth_validation
async def fetch_member_details(state: Annotated[dict, InjectedState], memGroupID, firstName="", lastName="", SSN=""):
    """Searches for List of members/dependents or individual member/dependents in cirrus. One of either first name, last name or SSN is required along with member group Id.
    
    Args:
        memberGroupID: (Required) A 7 or 8 digit numeric string representing the member group ID.
        firstName: (Optional either SSN or last name is provided) A string representing the first name of the member.
        lastName: (Optional either SSN or first name is provided) A string representing the last name of the member.
        SSN: (Optional either first name or last name is provided) A string representing the social security number of the member without "-".
    """
    # inquiryDate: (Optional) A string representing the date of inquiry in yyyy-mm-dd format.
    # addressType: (Optional) A string representing the address type of the member.
    try:
        user = state.get("user_info")
        clientId = user.get("client_id")
        uuid = user.get("uuid")
        params = CirrusMemberSearch(  
        memGroupID=memGroupID,  
        firstNameStartsWith=firstName,  
        lastNameStartsWith=lastName ,
        SSN= SSN,
        clientId = clientId,
        uuid = uuid
        )
        memberId = ""
        if params.SSN and not params.SSN.isspace() :
            final_response = await member_search_service.individual_member_search(params, memberId)
            return final_response
        elif (params.firstNameStartsWith and not params.lastNameStartsWith.isspace()) or (params.lastNameStartsWith and not params.lastNameStartsWith.isspace()):
            response = await member_search_service.member_search(params)
            members = response.get("responseData", {}).get("memberList", []) 
            if not members:
                return f"No members found in Cirrus for the provided data - Member Group ID: {memGroupID}, First Name: {firstName or None}, Last Name: {lastName or None}, SSN: {SSN or None}."
            memberId = members[0].get("demographics", {}).get('memberID')
            if(memberId):
                final_response = await member_search_service.individual_member_search(params, memberId)
                return final_response
            return f"No valid memberId found in members list for the provided data - Member Group ID: {memGroupID}, First Name: {firstName or None}, Last Name: {lastName or None}, SSN: {SSN or None}."
        else:
            return f"Can you provide me with a specific identifier, either first name, last name or SSN(social security number) for member group ID: {memGroupID} to narrow down the search results."
    except:
        await log_tool_error(state, traceback.format_exc(), "fetch_member_details")
        raise Exception (traceback.format_exc())
    
@tool    
async def trigger_pre_installation(
    state: Annotated[dict, InjectedState],
    groupId: str,                                                        
    effectiveDate: str ,      
    employBeginDate: str ,                                       
    firstName: str,
    lastName: str ,
    SSN: str ,
    DOB: str,
    gender: str ,
    employeeAddress: str,
    postalCode: str ,
    qualifyingEventDate: str ,
    planNames: Optional[list[str]] = [],
    dependents: Optional[list]=[],
    nameMiddle: Optional[str] = "" ,
    phoneNumber: Optional[str] = "" ,
    communicationText: Optional[str] = ""):
    
    """Collects data for pre-installation with parameters given by text or PDF upload. Member Installation/Enrollment starts at this tool.
    Required fields: (groupId, effectiveDate converted to mm-dd-yyyy format, employBeginDate in mm-dd-yyyy format, firstName, lastName, SSN, DOB, gender, employeeAddress, postalCode, qualifyingEventDate in mm-dd-yyyy format)
    Optional fields: (nameMiddle, phoneNumber, communicationText)
    Never ask for Plan and Population Details and bill group details at this step. If they have, extract each plan name from the document.
    Always ask if the user wants to add dependents with the required details before calling this tool. You can add multiple dependents: up to 1 spouse and 4 children.
    dependents: (Optional) [{("FirstName", "LastName", "SSN", "DOB", "Gender", "Type": "either spouse or child"): Required}, {...}]
    Do not assume any data, always ask for required data.
    """
    
    try:
        user = state.get("user_info")
        clientId = user.get("client_id")
        uuid = user.get("uuid")
        session_id = user.get("session_id")
        user_name = user.get("user_name")
        contract_options_response = await get_contract_options_service.get_contract_options(groupId, clientId, session_id, user_name, True, convert_date_format(effectiveDate))

        if len(planNames) > 0 and isinstance(contract_options_response, dict) and 'coverageDetails' in contract_options_response.keys():
            similar_plan_names = await get_contract_options_service.find_similar_plan_names(contract_options_response, planNames)
            print(similar_plan_names)
        return contract_options_response
    
    except Exception:
        await log_tool_error(state, traceback.format_exc(), "trigger_pre_installation")
        raise Exception (traceback.format_exc())
    
@tool  
async def check_member_status(
    state: Annotated[dict, InjectedState],
    groupId: str,                                      
    firstName: str,
    lastName: str ,
    memberId: Optional[str] = ""
    ):
    
    """Checks member's status if the installation was successful in cirrus or not with following parameters:
        groupId(Required), firstName(Required), lastName(Required), memberId(Optional)
    """
    try:
        user = state.get("user_info")
        clientId = user.get("client_id")
        response = await member_status_service.get_member_status(groupId, firstName, lastName, memberId, clientId)
        return response
    
    except Exception:
        await log_tool_error(state, traceback.format_exc(), "check_member_status")
        raise Exception (traceback.format_exc())
