from pydantic import BaseModel

class ToPlanRecommendationAssistant(BaseModel):
    """Transfers work to a specialized assistant to handle "Plan recommendation", "Shopping plan recommendation". It can also process pdf data."""
    
class ToCirrusAssistant(BaseModel):
    """Transfers work to a specialized assistant to handle every query related to "Group Inquiry", "Member Inquiry" """ #, "Member enrollment/Installation", "Member status check" in Cirrus. It can also process pdf data."""
    
class ToDataConverterAssistant(BaseModel):
    """Transfers work to a specialized assistant to handle queries related to data conversion. It can also process pdf data."""

class ToSurestAssistant(BaseModel):
    """Transfers all the work to a specialized assistant to handle every question related to surest plans, surest videos, any details related to surest, what can be done with surest, or surest training materials."""

class ToSamxOneAssistant(BaseModel):
    """Transfers all the work to a specialized assistant to handle every request or question related to create JSON from PDF from schema using schema type or any queries related to validation of binder-check."""

class ToDocumentSearchAssistant(BaseModel):
    """Transfers all the work to a specialized assistant to handle every request or question related to Broker N Employer(BNE) FAQ queries, document search, video search or any queries matching the strings `Broker Training`, `Employer Training`, or `Internal Training`. This agent can answer any question related to training materials and guides. Use this assistant for questions explicitly asking about BNE FAQs, document content, videos  or knowledge stored within searchable repositories."""
    
class ToPortalFormEnrollmentAssistant(BaseModel):
    """Transfers all the work to a specialized assistant to handle request or questions strictly related to form enrollment, member installation, or adding a member, No follow-ups are required for this agent. Fact: This assistant does not answer questions."""

router_tools = [ToPlanRecommendationAssistant.__name__, ToCirrusAssistant.__name__, ToDataConverterAssistant.__name__, ToSurestAssistant.__name__, ToSamxOneAssistant.__name__, ToDocumentSearchAssistant.__name__, ToPortalFormEnrollmentAssistant.__name__]