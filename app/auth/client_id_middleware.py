from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from services.get_client_ids_service import configs_client_id
from services.event_logger_service import logger
import traceback
from services import log_error_service
from config.db import get_database
from utils.general_utils import map_final_response

# List of endpoints that require client ID validation
CLIENT_ID_REQUIRED_ENDPOINTS = [
    "/generateResponse",
    "/generateResponseStargate",
    "/agenticOfflineEvaluation",
    # Add other endpoints that need client ID validation
]

class ClientIdValidationMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        path = request.url.path
        
        # Check if the current endpoint requires client ID validation
        if path in CLIENT_ID_REQUIRED_ENDPOINTS:
            try:
                # Get form data
                form_data = None
                if request.method == "POST":
                    form_data = await request.form()
                    client_id = form_data.get("client_id")
                    uuid = form_data.get("uuid", None)
                    user_name = form_data.get("user_name", None)
                    session_id = form_data.get("session_id", None)
                    
                    # If client_id is missing, return an error
                    if not client_id:
                        # Return early with error for missing client_id
                        await logger.error(uuid, user_name, session_id, None, "api", path, {"form_data": {k: v for k, v in form_data.items() if k != "pdf"}}, 
                                          None, 400, "Client ID is missing")
                        return JSONResponse(
                            status_code=400, 
                            content={"status": "error", "response": await map_final_response("Client ID is required")}
                        )
                    
                    # Validate the client ID
                    is_valid_client = await configs_client_id(client_id)
                    if not is_valid_client:
                        # Return early with error message if client ID is not valid
                        db = await get_database(client_id)
                        await log_error_service.log_error_to_db(
                            traceback.format_exc(), client_id, session_id, user_name, "Code Break", db
                        )
                        await logger.error(uuid, user_name, session_id, client_id, "api", path, 
                                          {"form_data": {k: v for k, v in form_data.items() if k != "pdf"}}, 
                                          None, 403, "Client not registered on Lingo")
                        return JSONResponse(
                            status_code=403,
                            content={"status": "error", "response": await map_final_response("The client you are requesting is not registered on Lingo yet. Use our onboarding environment if you want to register a new client")}
                        )
            except Exception as e:
                # Log any unexpected errors during validation
                traceback.print_exc()
                return JSONResponse(
                    status_code=500,
                    content={"status": "error", "response": await map_final_response("An error occurred while validating client ID")}
                )

        # Continue with the request if client ID is valid or validation is not required
        response = await call_next(request)
        return response