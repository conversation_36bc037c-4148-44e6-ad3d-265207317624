import base64
import json
import os
import httpx
import logging
from fastapi import HTTPException

def decode_jwt(token: str) -> dict:
    try:
        header, payload, signature = token.split(".")
        header_decoded = base64.urlsafe_b64decode(header + "==").decode('utf-8')

        payload_decoded = base64.urlsafe_b64decode(payload + "==").decode('utf-8')

        return json.loads(payload_decoded)
    except Exception as e:
        raise Exception("Invalid JWT Token")

async def authenticate_user(code: str, grant_type: str) -> dict:
    try:
        payload = {
            "grant_type": grant_type,
            "code": code,
            "redirect_uri": os.getenv('OIDC_REDIRECT_URI'),
            "client_id": os.getenv('OIDC_CLIENT_ID'),
            "client_secret": os.getenv('OIDC_CLIENT_SECRET'),
            "acr_values": os.getenv('OIDC_ACR_VALUE'),
        }

        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.post(os.getenv('TOKEN_SERVER_URL'), data=payload)
            if response.status_code == 200:
                token_data = response.json()
                access_token = token_data.get('access_token')
                id_token = token_data.get('id_token')

                if not access_token:
                    raise HTTPException(status_code=401, detail="Invalid token data")

                isAuthorized = True
                decoded_token = decode_jwt(access_token)
                decoded_id_token = decode_jwt(id_token)

                msid = decoded_token.get('msid')
                employeeId = decoded_token.get('employeeID')
                givenName = decoded_id_token.get('given_name')

                msad_groups = decoded_id_token.get('msad_groups', [])
                if isinstance(msad_groups, str):
                    msad_groups = [msad_groups]
                isAuthorized = "MagnusOEC_Users" in msad_groups

                return {
                    "isAuthorized": isAuthorized,
                    "msid": msid,
                    "employeeId": employeeId,
                    "givenName": givenName,
                }
            else:
                error_message = response.text
                logging.error(f"An unexpected error occurred: {response.status_code}, {error_message}")
                return {
                    "isAuthorized": False,
                }
    except httpx.HTTPStatusError as http_err:
        raise HTTPException(status_code=500, detail=f"HTTP error occurred: {http_err}")
    except httpx.RequestError as req_err:
        raise HTTPException(status_code=500, detail=f"Request error occurred: {req_err}")
    except Exception as e:
        raise HTTPException(status_code=500, detail="An unexpected error occurred")