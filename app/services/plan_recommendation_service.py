import httpx 
import base64  
import io  
import os
from services.event_logger_service import logger
import asyncio

# Define VERIFY_SSL once to be used throughout the file
VERIFY_SSL = os.environ.get('VERIFY_SSL') == 'True'

api_mapping = {  
    'employeesInfo_response': os.getenv("XTRACTOR_EMPLOYEES_API_URL"),  
    'groupInfo_response': os.getenv("XTRACTOR_GROUP_INFO_API_URL"),
    'renewalPlan_response': os.getenv("XTRACTOR_RENEWAL_PLAN_API_URL") ,
  
}  

async def get_shopping_plans(state):
    try:
        uuid = state.get("user_info")["uuid"]
        user_name = state.get("user_info")["user_name"]
        client_id = state.get("user_info")["client_id"]
        session_id = state.get("user_info")["session_id"]
        
        url = os.getenv("XTRACTOR_SHOPPING_PLAN_API_URL")
        
        async with httpx.AsyncClient(verify=VERIFY_SSL) as client:
            response = await client.get(url)
            status_code = response.status_code
            response_data = response.json()
            
            if str(status_code).startswith('2'):
                await logger.info(uuid, user_name, session_id, client_id, "api","plan_recommendation_api", None, response_data, status_code, url, None)
            else:
                await logger.error(uuid, user_name, session_id, client_id,"api", "plan_recommendation_api", None, response_data,status_code, url, None,)

            return response_data

    except httpx.HTTPStatusError as http_err:
        return {'error': f'HTTP error occurred: {http_err}'}
    except httpx.RequestError as req_err:
        return {'error': f'Request error occurred: {req_err}'}
    except ValueError as json_err:
        return {'error': f'JSON decoding error: {json_err}'}
  
  
async def get_plan_recommendation(pdf_data, state):  
    pdf_data = base64.b64decode(pdf_data)  
      
    async def post_url(url, data):  
        try:  
            pdf_file = io.BytesIO(pdf_data)  
            pdf_file.name = 'document.pdf'  
            files = {'pdf_file': (pdf_file.name, pdf_file, 'application/pdf')}  
              
            async with httpx.AsyncClient(verify=VERIFY_SSL) as client:  
                response = await client.post(url, files=files, data=data)  
                response.raise_for_status()  
                return response.json().get("data") if "get_company_details" not in url else response.json()  
        except httpx.HTTPStatusError as http_err:  
            return {'error': f'HTTP error occurred: {http_err}'}  
        except httpx.RequestError as req_err:  
            return {'error': f'Request error occurred: {req_err}'}  
        except ValueError as json_err:  
            return {'error': f'JSON decoding error: {json_err}'}  
  
    common_data = {'gpt_only': False}  
    results = {}  

      
    tasks = [post_url(url, common_data) for api_name, url in api_mapping.items()]  
    results_data = await asyncio.gather(*tasks, return_exceptions=True)  
      
    for (api_name, result_data) in zip(api_mapping.keys(), results_data):  
        if isinstance(result_data, Exception):  
            results[api_name] = {'error': str(result_data)}  
        else:  
            results[api_name] = result_data  
      
    uuid = state.get("user_info")["uuid"]  
    user_name = state.get("user_info")["user_name"] 
    client_id = state.get("user_info")["client_id"] 
    session_id = state.get("user_info")["session_id"] 
      
    await logger.info(uuid, user_name, session_id, client_id, "api", "plan_recommendation_api", "pdf_data", results, None, api_mapping)  
      
    return results