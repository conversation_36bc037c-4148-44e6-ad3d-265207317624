import os
import json
import logging
from datetime import datetime
from dateutil import tz  
import aiofiles

est = tz.gettz('US/Eastern')
ENV = os.getenv("ENV")

class EventLogger:

    def __init__(self, log_dir=f"./email/lingo_oec_logs_{ENV.lower()}"):
        """ Initialize the logger with a log directory """
        self.log_dir = log_dir
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)

        self.checkpoint_file = os.path.join(self.log_dir, 'checkpoint.json')  
        self.default_checkpoint = {"last_processed_index": 0, "yesterday_log_index": 0, "error_message": ""}  
          
        if not os.path.exists(self.checkpoint_file):  
            pass
            # with open(self.checkpoint_file, 'w') as f:  
            #     json.dump(self.default_checkpoint, f)  
        
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.DEBUG)

    def get_timestamp(self):
        """ Get current timestamp """
        return datetime.now(est).strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3]

    async def get_user_log_file(self):
        """ Generate a user-specific log file based on user info (e.g., user email or ID) """
        today = datetime.now().date()
        log_file = os.path.join(self.log_dir, f"{today}_log.json")
        return log_file

    async def load_existing_logs(self, log_file):
        """ Load existing logs from the file (if any), otherwise return an empty list """
        logs = []
        # if os.path.exists(log_file):
        #     async with aiofiles.open(log_file, 'r') as f:
        #         try:
        #             file_content = await f.read()
        #             logs = json.loads(file_content)
        #         except json.JSONDecodeError:
        #             logs = []
        # else:
        #     logs = []
        return logs

    async def save_logs(self, log_file, logs):
        """ Save logs back to the file as a list """
        pass
        # async with aiofiles.open(log_file, 'w') as f:
        #     file_content = json.dumps(logs, indent=4)
        #     await f.write(file_content)

    async def log(self, severity, session_id, user_info, platform, event_type, event_name, request, response, status_code, error_msg, url=None, feedback=None, feedback_notes=None):
        """ Core logging method """

        log_file = await self.get_user_log_file()
        
        log_data = {
            "severity": severity,
            "session_id": session_id,
            "user": user_info,
            "request_id": "", 
            "platform": platform,
            "time_stamp": self.get_timestamp(),
            "user_feedback": feedback if feedback else None,
            "feedback_notes": feedback_notes if feedback_notes else None,
            "event_type": event_type,
            "event_name": event_name,
            "url": url,
            "request": request,
            "response": response,
            "status_code": status_code,
            "error_msg": error_msg
        }

        existing_logs = await self.load_existing_logs(log_file)

        existing_logs.append(log_data)

        await self.save_logs(log_file, existing_logs)

    async def info(self, session_id, user_info, platform, event_type, event_name, request, response, status_code, error_msg, url=None, feedback=None):
        await self.log("INFO", session_id, user_info, platform, event_type, event_name, request, response, status_code, error_msg, url, feedback)

    async def warning(self, session_id, user_info, platform, event_type, event_name, request, response, status_code, error_msg, url=None, feedback=None):
        await self.log("WARN", session_id, user_info, platform, event_type, event_name, request, response, status_code, error_msg, url, feedback)

    async def error(self, session_id, user_info, platform, event_type, event_name, request, response, status_code, error_msg, url=None, feedback=None):
        await self.log("ERR", session_id, user_info, platform, event_type, event_name, request, response, status_code, error_msg, url, feedback)

    async def feedback(self, session_id, user_info, platform, event_type, event_name, request, response, status_code, error_msg, feedback, feedback_notes):
        await self.log("FB", session_id, user_info, platform, event_type, event_name, request, response, status_code, error_msg, None, feedback, feedback_notes)


logger = EventLogger()
