import os
import json
import logging
from datetime import datetime
from dateutil import tz  
import aiofiles
import httpx
import asyncio

  

est = tz.gettz('US/Eastern')
ENV = os.getenv("ENV")
kafka_url = os.getenv("LingoAI_Kafka_BASE_URL")
kafka_logger_enabled = os.getenv("KAFKA_Logger", "false").lower()

class EventLogger:

    def __init__(self, log_dir=f"./email/lingo_oec_logs_{ENV.lower()}"):
        """ Initialize the logger with a log directory """
        self.log_dir = log_dir
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)

        self.checkpoint_file = os.path.join(self.log_dir, 'checkpoint.json')  
        self.default_checkpoint = {"last_processed_index": 0, "yesterday_log_index": 0, "error_message": ""}  
          
        if not os.path.exists(self.checkpoint_file):  
            pass
            # with open(self.checkpoint_file, 'w') as f:  
            #     json.dump(self.default_checkpoint, f)  
        
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.DEBUG)

    def get_timestamp(self):
        """ Get current timestamp """
        return datetime.now(est).strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3]

    async def get_user_log_file(self):
        """ Generate a user-specific log file based on user info (e.g., user email or ID) """
        today = datetime.now().date()
        log_file = os.path.join(self.log_dir, f"{today}_log.json")
        return log_file

   
    async def log(self, severity, uuid, user_name, session_id, client_id, event_type, event_name, request, response, status_code, message, url=None, feedback=None, feedback_notes=None):
        """ Core logging method """

        log_file = await self.get_user_log_file()

        user_info = {
            "uuid": uuid,
            "user_name": user_name,
            "session_id": session_id,
            "client_id": client_id
        }

        log_data = {
            "severity": severity,
            "user": user_info,
            "request_id": "", 
            "time_stamp": self.get_timestamp(),
            "user_feedback": feedback if feedback else None,
            "feedback_notes": feedback_notes if feedback_notes else None,
            "event_type": event_type,
            "event_name": event_name,
            "url": url,
            "request": request,
            "response": response,
            "status_code": status_code,
            "message": message
        }
        if kafka_url and kafka_logger_enabled == "true":
            asyncio.create_task(self.send_log_to_kafka(log_data, kafka_url))
        
        
    async def info(self, uuid, user_name, session_id, client_id, event_type, event_name, request, response, status_code, message, url=None, feedback=None):
        await self.log("INFO", uuid, user_name, session_id, client_id, event_type, event_name, request, response, status_code, message, url, feedback)

    async def warning(self, uuid, user_name, session_id, client_id, event_type, event_name, request, response, status_code, message, url=None, feedback=None):
        await self.log("WARN", uuid, user_name, session_id, client_id, event_type, event_name, request, response, status_code, message, url, feedback)

    async def error(self, uuid, user_name, session_id, client_id, event_type, event_name, request, response, status_code, error_msg, url=None, feedback=None):
        await self.log("ERR", uuid, user_name, session_id, client_id, event_type, event_name, request, response, status_code, error_msg, url, feedback)

    async def feedback(self, uuid, user_name, session_id, client_id, event_type, event_name, request, response, status_code, message, feedback, feedback_notes):
        await self.log("FB", uuid, user_name, session_id, client_id, event_type, event_name, request, response, status_code, message, None, feedback, feedback_notes)

    async def send_log_to_kafka(self, log_data, kafka_url):
        """Send logs to the LingoAI Kafka project's API endpoint asynchronously"""
        try:
            kafka_url = f"{kafka_url}/logs"
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.post(kafka_url, json=log_data)
                response.raise_for_status()
                self.logger.info(f"Log sent to Kafka successfully: {response.json()}")
        except Exception as e:
            self.logger.error(f"Failed to send log to Kafka: {e}")

logger = EventLogger()
