from config.db import *

async def get_chat(session_id, offset, limit):
    
    collection = db['ChatConversation']
    
    pipeline = [  
    {"$match": {"session_id": session_id}},  
    {"$facet": {  
        "messages": [  
            {"$project": {  
                "conversation": 1,  
                "conversationLength": {"$size": "$conversation"}  
            }},  
            {"$project": {  
                "conversation": {  
                    "$slice": [  
                        "$conversation",  
                        {"$max": [0, {"$subtract": ["$conversationLength", {"$add": [offset, limit]}]}]},  
                        {"$min": [limit, {"$subtract": ["$conversationLength", offset]}]}  
                    ]  
                }  
            }},  
            {"$unwind": "$conversation"},  
            {"$replaceRoot": {"newRoot": "$conversation"}}  
        ],  
        "totalCount": [  
                {"$project": {  
                    "count": {"$size": "$conversation"}  
                }}  ]
    }}  
] 
    
    result = await collection.aggregate(pipeline).to_list(length=None)
    data = result[0] if result else None
    
    if data:  
        messages = data['messages']  
        total_count = data['totalCount'][0]['count'] if data['totalCount'] else 0  
        has_more = offset + limit < total_count  
    else:  
        messages = []  
        has_more = False  
    
    response = {'session_id': session_id, 'hasMore': has_more, 'conversation' : messages}     
    
    return response
