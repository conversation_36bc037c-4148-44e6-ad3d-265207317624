from config.db import *
from config.openAI import *
from utils.schemas.classes import generateTitleInterface
from langchain_core.messages import HumanMessage
from utils.general_utils import getTime

titles_collection = db["ChatTitles"]
    
async def summarize_text(user_input, chatbot_response, filename=None):
    if not user_input.strip() or user_input.strip() == "File Uploaded!":
        if filename:
            name = os.path.basename(filename)
            base = os.path.splitext(name)[0].replace("_", " ").title()
            return f"Uploaded: {base}"
        else:
            return "File Uploaded"
    shortened_response = " ".join(chatbot_response.split()[:100] ) 
    prompt = f"Generate a title from this query and response in 3-4 words without quotes around title: '{user_input} - {shortened_response}'"
    message = HumanMessage(content=prompt)
    response = await model.ainvoke([message])
    return response.content

async def update_title_db(input: generateTitleInterface, summary):
    current_time = getTime()
    if (await titles_collection.count_documents({'user_name': input.user_name}, limit=1) and input.edit==True):
        update_query = {"user_name": input.user_name, "titles.session_id" :  input.session_id}
        update_data = {"$set": {"titles.$.title": summary,  "update_timestamp": current_time}}
        await titles_collection.update_one(update_query, update_data)
          
