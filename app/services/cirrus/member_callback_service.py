import traceback
from services import log_error_service
from config.db import *
from utils.general_utils import getTime

collection = db["UspTransactions"]

def _get_status(callback_resp):  
    status = "ERROR" if any(msg['type'] == "ERROR" for msgList in callback_resp['messageList'] for msg in msgList['messages']) else "SUCCESS" 
    return status

async def save_member_callback(case_id, uuid, client_id, callback_resp):
    try:
        case_id = int(case_id)
        memberStatusToUpdate = {
            'employee_status': _get_status(callback_resp),
            'response': callback_resp
        }  
        current_time = getTime()
        data = {
            "members_status_in_Cirrus" : memberStatusToUpdate,
            "update_date": current_time,
            "status": _get_status(callback_resp)
        }
        await collection.update_one({"case_id" : case_id }, {"$set": data})
        return 'Member Update Success'  
    except Exception as e:
        db = await get_database(client_id)
        await log_error_service.log_error_to_db(traceback.format_exc(), client_id, uuid, "Code Break", db) 