import httpx
from config.jwt import get_token
import os
import json
from datetime import date
from services.log_cirrus_response_service import log_cirrus_response
from utils.schemas.cirrus_classes import *
from services.event_logger_service import logger

async def contract_options_api(memberGroupID, client_id, session_id, user_name):
    jwt_token = get_token()
    url = os.getenv("NIMBUS_URL_BASE") + "/contract-option/v1/read/"
    request_body = {
        "getEmployerGroupContractOptionRequest": {
            "memberGroupId": memberGroupID,
            "select": "All"
        }
    }
    data = json.dumps(request_body)

    async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
        response = await client.post(url, data=data, headers={"Authorization": "Bearer " + jwt_token, "Content-Type": "application/json"})
        status_code = response.status_code
        response_data = response.json()
        if str(status_code).startswith('2'):
            await logger.info(None, user_name, session_id, client_id, "api", "cirrus_contract_options_api", {"body": request_body}, response_data, status_code, None, url)
        else:
            await logger.error(None, user_name, session_id, client_id, "api", "cirrus_contract_options_api", {"body": request_body}, None, status_code, response_data, url)

        await log_cirrus_response(client_id, "Contract Options", url, status_code, response_data, session_id, user_name)
        return response_data

async def group_search_api(memberGroupID, client_id, session_id, user_name):
    url = os.getenv("CIRRUS_URL_BASE") + "/membergroups/v2.0"
    params = {'memgroupid': memberGroupID}
    jwt_token = get_token()

    async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
        response = await client.get(url, params=params, headers={"Authorization": "Bearer " + jwt_token, "Content-Type": "application/json"})
        status_code = response.status_code
        response_data = response.json()
        if str(status_code).startswith('2'):
            await logger.info(None, user_name, session_id, client_id, "api", "cirrus_group_search_api", params, response_data, status_code, None, url)
        else:
            await logger.error(None, user_name, session_id, client_id, "api", "cirrus_group_search_api", params, None, status_code, response_data, url)

        await log_cirrus_response(client_id, "Group Search", url, status_code, response_data, session_id, user_name, params)
        return response_data

async def memeber_search_api(input: CirrusMemberSearch):
    
    url = os.getenv("CIRRUS_URL_BASE") + "/get-member-list/v7.0"
    request_body = {
        "memGroupID": input.memGroupID,
        "inquiryDate": str(date.today())
    }
    
    if input.firstNameStartsWith and not input.firstNameStartsWith.isspace():
        request_body["firstNameStartsWith"] = input.firstNameStartsWith
        
    if input.lastNameStartsWith and not input.lastNameStartsWith.isspace():
        request_body["lastNameStartsWith"] = input.lastNameStartsWith

    data = json.dumps(request_body)
    jwt_token = get_token()

    async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
        response = await client.post(url, data=data, headers={"Authorization": "Bearer " + jwt_token, "Content-Type": "application/json"})
        status_code = response.status_code
        response_data = response.json()

        if str(status_code).startswith('2'):
            await logger.info(input.uuid, input.user_name, input.session_id, input.clientId, "api", "cirrus_member_search_api", {"body": request_body}, response_data, status_code, None, url)
        else:
            await logger.error(input.uuid, input.user_name, input.session_id, input.clientId, "api", "cirrus_member_search_api", {"body": request_body}, None, status_code, response_data, url)

        await log_cirrus_response(input.clientId, "Member Search", url, status_code, response_data, input.user_name, input.session_id, request_body)
        return response_data
