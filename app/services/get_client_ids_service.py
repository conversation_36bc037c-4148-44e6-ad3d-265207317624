from config.db import *

# Load client IDs synchronously during module initialization
collection = sync_db_onboarding["Configs"]
client_ids_document = collection.find_one({"type": "clientId"})
valid_client_ids = list(client_ids_document['clientId'].values()) if client_ids_document else []

async def configsClientIds(client_id: str):
    """
    Check if the provided client_id is valid.
    
    Args:
        client_id: The client ID to validate
        
    Returns:
        bool: True if the client_id is valid, False otherwise
    """
    return client_id in valid_client_ids

