from config.db import *
from datetime import datetime
from dateutil import tz  
from utils.general_utils import getTime

est = tz.gettz('US/Eastern')  

async def log_cirrus_response(clientID: str, type: str , url: str, status_code: int, response: str, session_id:str, user_name:str, request = "") -> None:
    try:
        db = await get_database(clientID)
        collection = "CirrusAPI"
        cirrus_collection = client[db][collection]  
        current_time = getTime()
        conversation = {
            "session_id": session_id,
            "user_name": user_name,
            "client_id": clientID,
            "type": type,
            "create_timestamp": current_time,
            "update_timestamp": current_time,
            "url": url,
            "status_code": status_code,
            "request": request,
            "response": response
        }
        await cirrus_collection.insert_one(conversation)
        
    except Exception as e:   
        print(f"An error occurred while logging the cirrus response in DB: {str(e)}")
