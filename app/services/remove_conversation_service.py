from config.db import *

chat_collection = db['ChatConversation']
titles_collection = db["ChatTitles"]

async def remove_convo(id, msid):
    if await chat_collection.count_documents({"id": id, 'msid': msid}, limit=1):
        delete_query = {"id": id}
        await chat_collection.delete_one(delete_query)  
    if await titles_collection.count_documents({'msid': msid}, limit=1):
        update_query = {"msid": msid}
        update_data = {"$pull": {"titles": {'id': id}}}
        await titles_collection.update_one(update_query, update_data)  
 
   
