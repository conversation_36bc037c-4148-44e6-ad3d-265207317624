from config.db import *

chat_collection = db['ChatConversation']
titles_collection = db["ChatTitles"]

async def remove_convo(session_id, user_name):
    if await chat_collection.count_documents({"session_id": session_id, 'user_name': user_name}, limit=1):
        delete_query = {"session_id": session_id}
        await chat_collection.delete_one(delete_query)  
    if await titles_collection.count_documents({'user_name': user_name}, limit=1):
        update_query = {"user_name": user_name}
        update_data = {"$pull": {"titles": {'session_id': session_id}}}
        await titles_collection.update_one(update_query, update_data)  
 
   
