from config.db import db_member_enroll
from config.xtractorDb import db_xtractorDocs
from services.event_logger_service import logger

async def update_and_retrieve_counter():
    """Increment the jobIdCounter in the counter collection and retrieve the updated value.
    Returns:
        int or None: The updated jobIdCounter value or None if an error occurs.
    """
    counter_collection = db_member_enroll["Counter"]

    try:
        # Increment the jobIdCounter in the counter collection
        counter_doc = await counter_collection.find_one_and_update(
            {},
            {"$inc": {"jobIDCounter": 1}},
            return_document=True
        )

        if not counter_doc:
            return None

        job_id_counter = counter_doc["jobIDCounter"]
        return job_id_counter
    except Exception as e:
        await logger.error(None, None, None, "api", "document_extraction", None, None, None, f"Error creating new enrollment job_id: {str(e)}")
        return None

async def update_and_retrieve_xtractorCounter():
    """Increment the jobIdCounter in the counter collection and retrieve the updated value.
    Returns:
        int or None: The updated jobIdCounter value or None if an error occurs.
    """
    counter_collection = db_xtractorDocs["Counter"]

    try:
        # Increment the jobIdCounter in the counter collection
        counter_doc = await counter_collection.find_one_and_update(
            {},
            {"$inc": {"jobIDCounter": 1}},
            return_document=True
        )

        if not counter_doc:
            return None

        job_id_counter = counter_doc["jobIDCounter"]
        return job_id_counter
    except Exception as e:
        await logger.error(None, None, None, "api", "document_extraction", None, None, None, f"Error creating new xtractor job_id: {str(e)}")
        return None
    
async def upsert_enrollment_document(job_id: int, update_data: dict):
    """
    Update or insert a document in the 'MemberEnrollment' collection.
    :param job_id: The job ID to filter the document to update. If None, a new document is inserted.
    :param update_data: A dictionary with the data to update or insert. This is required.
    :return: The updated or inserted document.
    """
    enrollment_collection = db_member_enroll["MemberEnrollment"]

    try:
        if job_id:
            filter = {"jobId": job_id}
            # Perform upsert if job_id is provided
            result = await enrollment_collection.update_one(filter, {'$set': update_data}, upsert=True)
            if result.upserted_id:
                await logger.info(None, None, None, "api", "document_extraction", update_data, None, None, None, None, f"Enrollment document with jobId: {job_id} inserted with id: {result.upserted_id}")
                # Retrieve the inserted document
                document = await enrollment_collection.find_one({"_id": result.upserted_id})
            else:
                await logger.info(None, None, None, "api", "document_extraction", update_data, None, None, None, None, f"Enrollment document with jobId: {job_id} updated.")
                # Retrieve the updated document
                document = await enrollment_collection.find_one(filter)
        else:
            # Insert new document if no job_id is provided
            result = await enrollment_collection.insert_one(update_data)
            await logger.info(None, None, None, "api", "document_extraction", update_data, None, None, None, None, f"New enrollment document inserted with jobId: {job_id}.")
            document = await enrollment_collection.find_one({"_id": result.inserted_id})

        return document
    except Exception as e:
        await logger.error(None, None, None, "api", "document_extraction", None, None, None, f"Error upserting enrollment document with job_id: {job_id}, error: {str(e)}")
        raise Exception(f'Error upserting document: {e}')


async def upsert_xtractor_document(job_id: int, update_data: dict):
    """
    Update or insert a document in the 'XtractorDocs' collection.
    :param job_id: The job ID to filter the document to update. If None, a new document is inserted.
    :param update_data: A dictionary with the data to update or insert. This is required.
    :return: The updated or inserted document.
    """
    xtractorDocs_collection = db_xtractorDocs["XtractorDocs"]

    try:
        if job_id:
            filter = {"jobId": job_id}
            # Perform upsert if job_id is provided
            result = await xtractorDocs_collection.update_one(filter, {'$set': update_data}, upsert=True)
            if result.upserted_id:
                await logger.info(None, None, None, "api", "document_extraction", update_data, None, None, None, None, f"Xtractor document with jobId: {job_id} inserted with id: {result.upserted_id}")
                # Retrieve the inserted document
                document = await xtractorDocs_collection.find_one({"_id": result.upserted_id})
            else:
                await logger.info(None, None, None, "api", "document_extraction", update_data, None, None, None, None, f"Xtractor document with jobId: {job_id} updated.")
                # Retrieve the updated document
                document = await xtractorDocs_collection.find_one(filter)
        else:
            # Insert new document if no job_id is provided
            result = await xtractorDocs_collection.insert_one(update_data)
            await logger.info(None, None, None, "api", "document_extraction", update_data, None, None, None, None, f"New xtractor document inserted with jobId: {job_id}.")
            document = await xtractorDocs_collection.find_one({"_id": result.inserted_id})

        return document
    except Exception as e:
        await logger.error(None, None, None, "api", "document_extraction", None, None, None, f"Error upserting xtractor document with job_id: {job_id}, error: {str(e)}")
        raise Exception(f'Error upserting document: {e}')
    
    
async def find_enrollment_document(job_id):
    """Find a document in the 'MemberEnrollment' collection.
    :param job_id: the job_id for the document to find
    :return: The retrieved document if found, otherwise None.
    """
    enrollment_collection = db_member_enroll["MemberEnrollment"]
    try:
        document = await enrollment_collection.find_one({"jobId": job_id})
        if document:
            return document
        else:
            await logger.warning(None, None, None, "api", "document_extraction", None, None, None, f"Can't find document with jobId: {job_id}")
            return None
    except Exception as e:
        await logger.error(None, None, None, "api", "document_extraction", None, None, None, f"Error finding document with jobId: {job_id}: {str(e)}")