from utils.schemas.classes import *
from config.db import *
from services import save_chat_conversation_external_service
from services.cirrus import install_member_service
from utils.general_utils import get_gpt_message_object, map_final_response
from graph.trigger_graph import run_assistant
import base64, re
from fastapi import UploadFile
from services.event_logger_service import logger
from utils.helpers.constants import OEC_DEFAULT_CLIENT_ID, OEC_SUREST_CLIENT_ID, OEC_BNEPORTAL_CLIENT_ID

async def generate_response(query: str, pdf: UploadFile, uuid:str, client_id: str, user_name: str, session_id: str, file_type: str, review_payload: str, additional_arg: dict, payload: dict):
    if not client_id or client_id.strip() == "":
        await logger.error(uuid, user_name, session_id, client_id, "api", "/generateResponse", payload, None, 400, "Client ID is missing.")
        return {"status": "error", "response": await map_final_response("It appears you don't have the right permissions to access the service. Please reach out to the administrator for assistance.")}
        

    #INFO: API request received
    await logger.info(uuid, user_name, session_id, client_id, "api", "/generateResponse", payload, None, None, None)
    if pdf and pdf.content_type == 'application/pdf': 
        response = await trigger_pdf_flow(query, file_type, pdf, uuid, user_name, session_id, client_id, review_payload, additional_arg)
        return {"status": "success", "response": await map_final_response(response)}
    elif query and not query.isspace():
        response = await trigger_query_flow(query, uuid, user_name, session_id, client_id, review_payload, additional_arg)
        return {"status": "success", "response": await map_final_response(response)}
    else:  
        #ERROR: Missing input
        await logger.error(uuid, user_name, session_id, client_id, "api", "/generateResponse", payload, None, 400, "It looks like we're missing some input. Could you provide a query or a PDF?")
        return {"status": "error", "response": await map_final_response("It looks like we're missing some input. Could you provide a query or a PDF?")}

async def trigger_pdf_flow(query: str, file_type: str, pdf: UploadFile, uuid: str, user_name: str, session_id: str, client_id: str, review_payload: str, additional_arg: dict):
    if not file_type:  
        #ERROR: Required parameter file type is missing!!
        await logger.error(uuid, user_name, session_id, client_id, "function", "trigger_pdf_flow", None, None, 400, "Required parameter file type is missing!!")
        return {"status": "error", "response": await map_final_response("Required parameter file type is missing!!")}
    
    if file_type not in ["enrollment-form","create-structure"]:
        #ERROR: Currently we are supporting only structured pdf extraction, member install and enrollment form as feed.
        await logger.error(uuid, user_name, session_id, client_id, "function", "trigger_pdf_flow", None, None, 400, "Currently we are supporting only structured pdf extraction, member install and enrollment form as feed.")
        return {"status": "error", "response": await map_final_response("Currently we are supporting only structured pdf extraction, member install and enrollment form as feed.")}
    
    pdf_bytes = await pdf.read()
    encoded_pdf = base64.b64encode(pdf_bytes).decode('utf-8')

    # INFO: for create-structure, we don't need to pass the pdf file to agent, will be handled by tool
    if (file_type == "create-structure"):
        base64_message_obj = await get_gpt_message_object(query)   
    else:
        base64_message_obj = await get_gpt_message_object(pdf_bytes)
    response = await run_assistant(base64_message_obj, uuid, user_name, session_id, client_id, review_payload, encoded_pdf, additional_arg=additional_arg)
    
    if file_type == "enrollment-form":
        await install_member_service.install_memberData(uuid, client_id, file_type, pdf_bytes, response)
    if (client_id!="Internal"):
        conversation = {"request": query, "response": response}
        await save_chat_conversation_external_service.store_chat_external(session_id, client_id, conversation, client[db_name_start + "_" + client_id])
    return response


async def trigger_query_flow(query: str, uuid: str, user_name: str, session_id: str, client_id: str, review_payload: str, additional_arg: dict):
    message_obj = await get_gpt_message_object(query)
    response = await run_assistant(message_obj, uuid, user_name, session_id, client_id, review_payload, additional_arg=additional_arg)

    if (client_id!=OEC_DEFAULT_CLIENT_ID and client_id!=OEC_SUREST_CLIENT_ID ):
        conversation = {"request": query, "response": response}
        await save_chat_conversation_external_service.store_chat_external(session_id, client_id, conversation, client[db_name_start + "_" +client_id])
    return response