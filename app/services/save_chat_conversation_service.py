from config.db import *  
from utils.general_utils import getTime
from utils.schemas.classes import saveChatInterface
   
chat_collection = db['ChatConversation'] 
titles_collection = db['ChatTitles']



async def store_chat(input: saveChatInterface): 
    try:
        current_time = getTime()
        # Save the title
        if not await titles_collection.find_one({'titles': {'$elemMatch': {'session_id': input.session_id}}}):
            new_title = {"session_id":input.session_id, "title":input.title, "create_timestamp": current_time, "update_timestamp": current_time}
            result = await titles_collection.update_one(
                {       
                    "user_name": input.user_name,
                },
                {
                    "$push": {
                        "titles": new_title
                    },
                        "$set": {
                            "update_timestamp": current_time 
                        }
                }) 

            # If the user_name is not found, insert a new document
            if result.modified_count == 0:
                new_document = {
                    "user_name": input.user_name,
                    "titles": [new_title],
                    "create_timestamp": current_time,
                    "update_timestamp": current_time
                }
                insert_result = await titles_collection.insert_one(new_document)
                if not insert_result.acknowledged:
                    print("Failed to insert new title")

        current_time = getTime()
        existing_chat = await chat_collection.find_one({"session_id": input.session_id})

        # Save chat conversation
        if existing_chat:
            current_conversation = existing_chat["conversation"]
            num_existing_chats = len(current_conversation)
            replace_start_index = max(0, num_existing_chats - input.offset)
            updated_conversations = (current_conversation[:replace_start_index] + input.conversation)
                    
            await chat_collection.update_one({"session_id": input.session_id}, {"$set": {"conversation": updated_conversations, "update_timestamp": current_time}})
        else:
            await chat_collection.insert_one({  
                "session_id": input.session_id,
                "create_timestamp": current_time,  
                "update_timestamp": current_time,  
                "uuid": input.uuid,
                "user_name": input.user_name,    
                "conversation": input.conversation  
            }) 
    except Exception as e:    
        raise e  
