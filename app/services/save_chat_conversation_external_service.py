from datetime import datetime  
from dateutil import tz  
  
est = tz.gettz('US/Eastern')  
  
async def store_chat_external(session_id, client_id, conversation, external_db):  
    current_time = datetime.now(est).strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3]  
    chat_collection = external_db['ChatConversation']  
    if await chat_collection.count_documents({"session_id": session_id}, limit=1):  
        update_query = {"session_id": session_id}  
        update_data = {  
            "$push": {"conversation": conversation},  
            "$set": {"update_date": current_time}  
        }  
        await chat_collection.update_one(update_query, update_data)  
    else:  
        await chat_collection.insert_one({  
            "create_timestamp": current_time,  
            "update_timestamp": current_time,
            "session_id": session_id,  
            "client_id": client_id,  
            "conversation": [conversation]  
        })     
