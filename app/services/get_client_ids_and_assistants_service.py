from config.db import *

# Load client IDs and assistant routers synchronously during module initialization
collection = sync_db_onboarding["Configs"]

client_id_document = collection.find_one({"type": "client_id"})
if client_id_document and "client_id" in client_id_document:
    valid_client_ids = [client_id for client_id, enabled in client_id_document["client_id"].items() if enabled]
else:
    valid_client_ids = []

client_assistant_mapper_document = collection.find_one({"type": "client_assistant_mapper"})
if client_assistant_mapper_document and "list" in client_assistant_mapper_document:
    valid_assistants = {list(entry.keys())[0]: [assistant for assistant, enabled in list(entry.values())[0].items() if enabled] for entry in client_assistant_mapper_document["list"]}
else:
    valid_assistants = {}

async def validate_client_id(client_id: str):
    """
    Check if the provided client_id is valid.
    
    Args:
        client_id: The client ID to validate
        
    Returns:
        bool: True if the client_id is valid, False otherwise
    """
    return client_id in valid_client_ids

async def validate_assistant(client_id: str, assistant: str):
    """
    Check if the provided assistant is valid for the given client_id.
    
    Args:
        client_id: The validated client ID attempting to access the assistant
        assistant: The name of the assistant or its router tool to validate
        
    Returns:
        bool: True if the assistant is valid for the client_id, False otherwise
    """
    if client_id in valid_assistants:
        return assistant in valid_assistants[client_id]
    return False
