from config.db import *
from datetime import datetime
from utils.general_utils import getTime

theme_collection = db["UserPreference"]

async def update_theme_db(msid, theme):
    # Append The New Theme & Save It To The Database
    current_time = getTime()
    result = await theme_collection.find_one_and_update(
        {
            "msid": msid,
        },
        {
            "$set": {
                # "MSID": msid,
                "theme": theme,
                "update_timestamp": current_time
            }
        }
    )
    # If The MSID Is Not Found, Insert A New Document
    if result is None:
            new_document = { 
                "msid": msid,
                "theme": theme,
                "create_timestamp": current_time, 
                "update_timestamp": current_time  
            }
            insert_result = await theme_collection.insert_one(new_document)
            if not insert_result.acknowledged:
                print("Failed To Insert New Theme")
            
async def get_preference(msid):
    collection = db['UserPreference']
    query = {'msid': msid}  
    query_result = await collection.find_one(query)  
    current_time = getTime()
    if not query_result:  
        # If msid is not found, insert a new document with 'theme': 'uhc'  
        new_document = {'theme': 'uhc', 'msid': msid, 'create_timestamp': current_time, 'update_timestamp': current_time}  
        await collection.insert_one(new_document)  
        preference = {'theme': 'uhc'}  
    else:  
        preference = {'theme': query_result.get('theme')}  
  
    return preference
