from config.db import *
from datetime import datetime
from utils.helpers.constants import *
from utils.general_utils import getTime

feedback_collection = db['UserChatFeedback']

async def store_feedback(id, msid, index, submittedData, chatbotResponse, feedback):
    current_timestamp = getTime()
    # Determine whether a conversation entry with the given id exists
    if await feedback_collection.count_documents({"id": id}, limit=1):
        # Empty feedback
        # Remove the message from the conversation entry
        if feedback == "":
            await feedback_collection.update_one(
                {"id": id},
                {"$pull": {"data": {"index": index, "submittedData": submittedData, "chatbotResponse": chatbotResponse}}}
            )
            
            document = await feedback_collection.find_one({"id": id})
            if document and len(document.get("data", [])) == 0:
                await feedback_collection.delete_one({"id": id})
            return
        
        # Non-empty feedback
        # Check if an entry with the given index, submittedData, and chatbotResponse exists
        existing_entry = await feedback_collection.find_one({
            "id": id,
            "data": {
                "$elemMatch": {
                    "index": index,
                    "submittedData": submittedData,
                    "chatbotResponse": chatbotResponse
                }
            }
        })
        
        if existing_entry:
            await feedback_collection.update_one(
                {
                    "id": id,
                    "data.index": index,
                    "data.submittedData": submittedData,
                    "data.chatbotResponse": chatbotResponse
                },
                {"$set": {"data.$.feedback": feedback, "update_timestamp": current_timestamp}}
            )
        else:
            update_query = {"id": id}
            feedback_data = {"index": index, "submittedData": submittedData, "chatbotResponse": chatbotResponse, "feedback": feedback}
            update_data = {"$push": {"data": feedback_data}}
            await feedback_collection.update_one(update_query, update_data)
    else:
        feedback_list = {"id": id, "msid": msid, "create_timestamp": current_timestamp, "update_timestamp": current_timestamp, "data": []}
        feedback_list["data"].append({"index": index, "submittedData": submittedData, "chatbotResponse": chatbotResponse, "feedback": feedback})
        await feedback_collection.insert_one(feedback_list)
