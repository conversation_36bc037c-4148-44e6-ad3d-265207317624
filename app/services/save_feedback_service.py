from config.db import *
from datetime import datetime
from utils.helpers.constants import *
from utils.general_utils import getTime

feedback_collection = db['UserChatFeedback']

async def store_feedback(user_name, uuid,session_id, index, submittedData, chatbotResponse, feedback, feedback_category=None, feedback_details=None, documents=None):
    current_timestamp = getTime()
    # Determine whether a conversation entry with the given id exists
    if await feedback_collection.count_documents({"session_id": session_id}, limit=1):
        # Empty feedback
        # Remove the message from the conversation entry
        if feedback == "":
            await feedback_collection.update_one(
                {"session_id": session_id},
                {"$pull": {"data": {"index": index, "submittedData": submittedData, "chatbotResponse": chatbotResponse}}}
            )
            
            document = await feedback_collection.find_one({"session_id": session_id})
            if document and len(document.get("data", [])) == 0:
                await feedback_collection.delete_one({"session_id": session_id})
            return
        
        # Non-empty feedback
        # Check if an entry with the given index, submittedData, and chatbotResponse exists
        existing_entry = await feedback_collection.find_one({
            "session_id": session_id,
            "data": {
                "$elemMatch": {
                    "index": index,
                    "submittedData": submittedData,
                    "chatbotResponse": chatbotResponse
                }
            }
        })
        
        if existing_entry:
            update_data = {
                "data.$.feedback": feedback,
                "update_timestamp": current_timestamp
            }
            
            # Add new fields if provided
            update_data["data.$.feedback_category"] = feedback_category
            update_data["data.$.feedback_details"] = feedback_details
            update_data["data.$.documents"] = documents
                
            # Fixed query - use the $elemMatch operator to properly identify the array element
            await feedback_collection.update_one(
                {
                    "session_id": session_id,
                    "data": {
                        "$elemMatch": {
                            "index": index,
                            "submittedData": submittedData,
                            "chatbotResponse": chatbotResponse
                        }
                    }
                },
                {"$set": update_data}
            )
        else:
            update_query = {"session_id": session_id}
            feedback_data = {
                "index": index, 
                "submittedData": submittedData, 
                "chatbotResponse": chatbotResponse, 
                "feedback": feedback
            }
            
            # Add new fields if provided
            feedback_data["feedback_category"] = feedback_category
            feedback_data["feedback_details"] = feedback_details
            feedback_data["documents"] = documents
                
            update_data = {"$push": {"data": feedback_data}}
            await feedback_collection.update_one(update_query, update_data)
    else:
        feedback_list = {"user_name": user_name,"uuid":uuid,"session_id":session_id , "create_timestamp": current_timestamp, "update_timestamp": current_timestamp, "data": []}
        feedback_data = {
            "index": index, 
            "submittedData": submittedData, 
            "chatbotResponse": chatbotResponse, 
            "feedback": feedback
        }
        
        # Add new fields if provided
        feedback_data["feedback_category"] = feedback_category
        feedback_data["feedback_details"] = feedback_details
        feedback_data["documents"] = documents
            
        feedback_list["data"].append(feedback_data)
        await feedback_collection.insert_one(feedback_list)
