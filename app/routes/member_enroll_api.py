import base64
import copy
import traceback
from config.member_enroll_logging_config import member_enroll_logger
from fastapi import APIRouter, BackgroundTasks, Form, Request, File, UploadFile
from fastapi.responses import JSONResponse
from services import log_error_service
from services.member_enroll import (
    member_enroll_callback_service,
    member_enroll_extract_service,
)
from utils.schemas.mapping.member_enroll_classes import CallbackData
from utils.helpers.constants import OEC_DEFAULT_CLIENT_ID
from config.db import get_database

router = APIRouter()


@router.post("/docExtract")
async def document_extraction(
    request: Request,
    background_tasks: BackgroundTasks,
    clientId: str = Form(None),
    uuid: str = Form(None),
    query: str = Form(None),
    callbackAPI: str = Form(None),
    version: str = Form("1.0"),
    file: UploadFile = File(None),
):
    if request.headers.get("Content-Type") == "application/json":
        body = await request.json()
        json_data = {
            "clientId": body.get("clientId"),
            "uuid": body.get("uuid"),
            "query": body.get("query"),
            "callbackAPI": "".join(body.get("callbackAPI").split()),
            "version": body.get("version", "1.0"),
            "fileBase64": body.get("file"),
            "fileName": body.get("fileName")
        }        
        if body.get("file") is None or body.get("fileName") is None:
            response = {
                "status": "Error",
                "message": "File content and file name is required for base64 content.",
            }
            return JSONResponse(status_code=400, content=response)
    else:
        json_data = {
            "clientId": clientId,
            "uuid": uuid,
            "query": query,
            "callbackAPI": "".join(callbackAPI.split()),
            "version": version
        }
        
    request_data_copy = copy.deepcopy(json_data)
    if 'fileBase64' in request_data_copy:
        del request_data_copy['fileBase64']

    member_enroll_logger.info("Received request for document extraction with content: %s", request_data_copy)

    response = {}
    status_code = 200

    if json_data.get("fileBase64") is None and file is None:
        response = {
            "status": "Error",
            "message": "No file content provided.",
        }
        return JSONResponse(status_code=400, content=response)
    
    if json_data.get("query").lower() == "enrollment" and json_data.get("uuid") is None:
        response = {
            "status": "Error",
            "message": "UUID is required for enrollment.",
        }
        return JSONResponse(status_code=400, content=response)


    if (json_data.get("query").lower() == "enrollment" and json_data.get("clientId") in ["BNE_MOBILE", "BNE_PORTAL"]) or json_data.get("query").lower() != "enrollment":
        if file:
            file_content = await file.read()
            json_data["fileName"] = file.filename
        else:
            file_content = base64.b64decode(json_data.get("fileBase64"))
        try:
            status_code, response = (
                await member_enroll_extract_service.handle_document_extraction(
                    file_content, json_data, background_tasks
                )
            )
        except Exception as e:
            traceback.print_exc()
            db = await get_database(OEC_DEFAULT_CLIENT_ID)
            await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, None, "Code Break", db)
            response = {
                "status": "Error",
                "message": "Unexpected process error occurred."
            }
            return JSONResponse(status_code=500, content=response)


    else:
        member_enroll_logger.error("Unsupported client type: %s", json_data.get("clientId"))
        response = {
            "status": "Error",
            "message": f"Unsupported client type {json_data.get('clientId')}. Please use BNE_MOBILE or BNE_PORTAL for enrollment.",
        }
        status_code = 400

    return JSONResponse(status_code=status_code, content=response)


@router.post("/docExtractionCallback")
async def document_extraction_callback(data: CallbackData, background_tasks: BackgroundTasks):
    request_data = data.dict()

    # Log the request parameters minus the payload to prevent cluttered logs
    request_data_copy = copy.deepcopy(request_data)
    if 'payload' in request_data_copy:
        del request_data_copy['payload']

    member_enroll_logger.info("Received callback data: %s", request_data_copy)

    try:
        status_code, response = await member_enroll_callback_service.handle_callback(
            request_data, background_tasks
        )
    except Exception as e:
        traceback.print_exc()
        db = await get_database(OEC_DEFAULT_CLIENT_ID)
        await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, None, "Code Break", db)
        response = {
            "status": "Error",
            "message": "Unexpected process error occurred."
        }
        return JSONResponse(status_code=500, content=response)
               
    member_enroll_logger.info("Response for document extraction callback: %s", response)
    return JSONResponse(status_code=status_code, content=response)
