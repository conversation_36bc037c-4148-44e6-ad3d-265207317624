import json
from fastapi import APIRouter, Request
import traceback
import os
import httpx

router = APIRouter()

async def fetch_gco_reports_data(uuid: str, user_type: str = "EXTERNAL"):
    try:
        
        initial_url = os.environ.get('GCO_PARSER_URL')
        user_type = user_type.upper() if user_type else 'EXTERNAL'
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
        
            if(user_type == 'EXTERNAL'):
                endpoint =  initial_url + "external/authorisation"  # Replace with the actual API endpoint
                headers = {
                        "Content-Type": "application/json",
                    }
                body = {
                            "uuid": uuid,
                            "caller": "ssr"
                        }
                
                response = await client.post(endpoint, json=body, headers=headers)
                
            elif(user_type == 'INTERNAL'):
                # USER_TYPE is INTERNAL
                endpoint =  initial_url + "internal/authorisation"  # Replace with the actual API endpoint
                headers = {
                        "Content-Type": "application/json",
                    }
                
                response = await client.get(endpoint, headers=headers)
            else:
                return {"status": "error", "response": "Invalid user type. Please use 'EXTERNAL' or 'INTERNAL'."}
                
            if response.status_code == 200:
                response = response.json()  # Parse the JSON response
                
                ####### redis dump ########
                redis_url = os.environ.get('GCO_REDIS_URL') + 'put'
                payload = {
                        "key": "GCO:" + uuid,
                        "timeUnit": "DAYS",
                        "value": json.dumps(response),
                        "expiration": "1"
                    }
                
                await client.post(redis_url, json=payload, headers=headers)
                    
                return {"status": "success", "response": response}
            else:
                return {"status": "error", "response": f"Failed to fetch data: {response.text}"}
                        # Simulating a successful response for demonstration purposes
    except Exception as e:
        traceback.print_exc()
        return {"status": "error", "response": f"An unexpected error occurred: {str(e)}"}

@router.post("/getWelcomePageData")
async def generate_welcome_page_data(request: Request):
    data = await request.json()
    uuid = data.get("uuid", "")
    user_type = data.get("user_type", "EXTERNAL")
    result = await fetch_gco_reports_data(uuid, user_type)
    return result

async def store_user_tracking_info(uuid: str, action_type: str, user_type: str = "External", user_info: dict = None):
    try:
        initial_url = os.environ.get('GCO_PARSER_URL')
        endpoint = initial_url, "storeusertrackinginfo"
            # Try to fetch uuid from state, fallback to state['user_info']['uuid'] if not found
        body = {
            "user_id": uuid,
            "user_type": user_type,
            "user_access_role": "",
            "user_ext_type": "",
            "action_type": action_type,
            "incident_number": "",
            "group_id": "",
            "group_name": "",
            "user_name": "",
            "user_email": "",
            "file_name": "",
            "report_type": "",
            "published_at":"",
            "platform_id": "",
            "funding_type": "",
            "session_id": ""    #is needed for tracking purposes?
        }
        headers = {
                        "Content-Type": "application/json",
                    }
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.post(endpoint, json=body, headers=headers)
            if response.status_code == 200: 
                # new_data = response.json()
                print("Successfully logged user activity")
            else:
                print (f"Failed to logged user activity: {response.text}")
    except Exception as e:
        traceback.print_exc()
        print(f"An unexpected error occurred while storing user tracking info: {str(e)}")
