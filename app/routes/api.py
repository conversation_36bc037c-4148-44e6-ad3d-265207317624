from fastapi import APIRouter, File, UploadFile, Form, HTTPException
from fastapi.responses import JSONResponse
from utils.schemas.classes import *
from config.db import *
import json
from services.event_logger_service import logger
from services.user_auth_service import authenticate_user
from services.ohid_user_auth_service import ohid_authenticate_user, generate_token, refresh_token
from services import generate_response_service, generate_title_service, get_title_service, save_feedback_service, save_chat_conversation_service, get_chat_service, remove_conversation_service, profile_service, log_error_service, transcription_service, offline_evaluation_service, get_allowed_origins_service
from utils.helpers.constants import OEC_DEFAULT_CLIENT_ID
from utils.general_utils import map_final_response
from services.get_client_ids_and_assistants_service import validate_client_id
from utils.request_utils import parse_additional_args
import traceback
import io
import os   
from client_plugins.surest.surest_utils.utils import create_or_update_surest_training_video, create_or_update_surest_training_material , delete_surest_training_material, get_all_surest_training_materials
from graph.state import get_postgres_checkpointer

router = APIRouter()

@router.post("/userAuth")
async def user_auth(
    code: str = Form(...),
    grant_type: str = Form(...),
):
    try:
        return await authenticate_user(code, grant_type)
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail="An unexpected error occurred")

@router.post("/OhidLogin")
async def ohid_user_auth(
    code: str = Form(...),
    grant_type: str = Form(...),
):
    try:
        return await ohid_authenticate_user(code, grant_type)
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail="An unexpected error occurred")

@router.post("/generateLingoToken")
async def generate_lingo_token(
    client_id: str = Form(...),
    token: str = Form(...),
):
    try:
        return await generate_token(client_id, token)
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail="An unexpected error occurred")
    
@router.post("/refreshLingoToken")
async def refresh_lingo_token(
    client_id: str = Form(...),
    token: str = Form(...),
):
    try:
        return await refresh_token(client_id, token)
    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail="An unexpected error occurred")

@router.post("/generateTitle")
async def generate_title(input: generateTitleInterface):
    try:
        if (input.edit==False):
            summary = await generate_title_service.summarize_text(input.user_input, input.chatbot_response, input.filename)
        else:
            summary = input.user_input
            await generate_title_service.update_title_db(input, summary)
        return {"status": "success", "title": summary}
    except Exception as e:
        traceback.print_exc()
        db = await get_database(OEC_DEFAULT_CLIENT_ID)
        await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, input.session_id, input.user_name, "Code Break", db, input.uuid)
        return {"status":"error", "title": "Error - some unexpected error occurred"}

@router.post("/getTitles")
async def get_titles(input: getTitleInterface):
    try:
        title_list = await get_title_service.get_title(input.uuid)
        return title_list
    except Exception as e:
        traceback.print_exc()
        db = await get_database(OEC_DEFAULT_CLIENT_ID)
        await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, input.session_id, input.user_name, "Code Break", db, input.uuid)
        return {"status":"error"}

@router.post("/generateResponse")
async def generate_response(query: Optional[str] = Form(None),
                      regenerate: Optional[str] = Form(None),
                      pdf: Optional[UploadFile] = File(None),
                      uuid: Optional[str] = Form(None),
                      client_id: str = Form(...),
                      user_name: str = Form(...),
                      session_id: str = Form(...),  
                      file_type: Optional[str] = Form(None),
                      review_payload: Optional[str] = Form(None),
                      additional_arg: Optional[str] = Form(None)):
    
    additional_arg_dict = parse_additional_args(additional_arg, field_name="additional_arg")

    payload = {
        "query": query,
        "regenerate": regenerate,
        "pdf": pdf.filename if pdf else None,
        "uuid": uuid if uuid else None,
        "client_id": client_id,
        "user_name": user_name,
        "session_id": session_id,
        "file_type": file_type,
        "review_payload": review_payload,
        "additional_arg": additional_arg_dict
    }

    
    is_valid_client = await validate_client_id(client_id)
    if not is_valid_client:
        # Return early with error message if client ID is not valid
        await logger.error(uuid, user_name, session_id, client_id, "api", "/generateResponse", payload, None, 403, "Client not registered on Lingo")
        return JSONResponse(
        status_code=200,
        content={"status": "success", "response": await map_final_response("The client / capability you are requesting is not registered on Lingo yet or not part of the current release. Please reach out to the Lingo Support for assistance.")}
    )
    else:
        try:
            # Only proceed with response generation if client ID is valid
            return await generate_response_service.generate_response(query, pdf, uuid, client_id, user_name, session_id, file_type, review_payload, additional_arg_dict, payload)
        except Exception as e:
            traceback.print_exc() 
            db = await get_database(client_id)
            await log_error_service.log_error_to_db(traceback.format_exc(), client_id, session_id, user_name, "Code Break", db, uuid)
            #ERROR:
            await logger.error(uuid, user_name, session_id, client_id, "api", "/generateResponse", payload, None, 500, str(e))
            return {"status": "error", "response": await map_final_response("Oops! Something went wrong while generating the answer.")}


@router.post("/generateResponseStargate")
async def generate_response_stargate(query: Optional[str] = Form(None),
                      regenerate: Optional[str] = Form(None),
                      pdf: Optional[UploadFile] = File(None),
                      uuid: Optional[str] = Form(None),
                      client_id: str = Form(...),
                      user_name: str = Form(...),
                      session_id: str = Form(...),  
                      file_type: Optional[str] = Form(None),
                      review_payload: Optional[str] = Form(None),
                      additional_arg: Optional[str] = Form(None)):
    try:
        additional_arg_dict = json.loads(additional_arg) if additional_arg else None  
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON format for additional_arg")
    
    payload = {
        "query": query,
        "regenerate": regenerate,
        "pdf": pdf.filename if pdf else None,
        "uuid": uuid if uuid else None,
        "client_id": client_id,
        "user_name": user_name,
        "session_id": session_id,
        "file_type": file_type,
        "review_payload": review_payload,
        "additional_arg": additional_arg_dict
    }
    
    is_valid_client = await validate_client_id(client_id)
    if not is_valid_client:
        # Return early with error message if client ID is not valid
        await logger.error(uuid, user_name, session_id, client_id, "api", "/generateResponse", payload, None, 403, "Client not registered on Lingo")
        return JSONResponse(
        status_code=200,
        content={"status": "success", "response": await map_final_response("The client / capability you are requesting is not registered on Lingo yet or not part of the current release. Please reach out to the Lingo Support for assistance.")}
    )
    else:
        try:
            # Only proceed with response generation if client ID is valid
            return await generate_response_service.generate_response(query, pdf, uuid, client_id, user_name, session_id, file_type, review_payload, additional_arg_dict, payload)
        except Exception as e:
            traceback.print_exc() 
            db = await get_database(client_id)
            await log_error_service.log_error_to_db(traceback.format_exc(), client_id, session_id, user_name, "Code Break", db, uuid)
            #ERROR:
            await logger.error(uuid, user_name, session_id, client_id, "api", "/generateResponse", payload, None, 500, str(e))
            return {"status": "error", "response": await map_final_response("Oops! Something went wrong while generating the answer.")}

@router.post("/saveChatConversation")
async def save_chat_conversation(input: saveChatInterface):
    try: 
        await save_chat_conversation_service.store_chat(input)
        return {"status": "success"}
    except: 
        traceback.print_exc()
        db = await get_database(OEC_DEFAULT_CLIENT_ID)
        await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, input.session_id, input.user_name, "Code Break", db, input.uuid)
        return {"status": "error"}

@router.post("/getChatConversation")
async def get_chat_conversation(input: getChatInterface):
    try:
        convo = await get_chat_service.get_chat(input.session_id, input.offset, input.limit)
        return {"status": "success", "convo": convo}
    except:
        traceback.print_exc()
        db = await get_database(OEC_DEFAULT_CLIENT_ID)
        await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, input.session_id, input.user_name,"Code Break", db, input.uuid)
        return {"status": "error", "response": "Error while getting chat..."}

@router.post("/fileUpload", deprecated=True)
async def create_upload_file(file: fileUploadInterface):
    success_message="The route is not active."
    return success_message

@router.post("/saveFeedback")
async def save_feedback(user_name: str = Form(...),uuid: str = Form(...),session_id: str = Form(...),
    index: int = Form(...), subindex: int = Form(...),submittedData: str = Form(...),chatbotResponse: str = Form(...),feedback: str = Form(...),feedback_category: Optional[str] = Form(None),feedback_details: Optional[str] = Form(None),
    documents: Optional[UploadFile] = File(None)):
    try:
        chatbotResponse=json.loads(chatbotResponse)
        document_data = None
        if documents:
            content = await documents.read()
            document_data = {
                "filename": documents.filename,
                "content_type": documents.content_type,
                "size": len(content),
                "content": content  # Binary data
            }
        await save_feedback_service.store_feedback(
            user_name, 
            uuid,
            session_id,
            f"{index}, {subindex}", 
            submittedData, 
            chatbotResponse, 
            feedback,
            feedback_category,
            feedback_details,
            document_data
        )
        return {"status": "success"}
    except:
        traceback.print_exc()
        db = await get_database(OEC_DEFAULT_CLIENT_ID)
        await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID,session_id, user_name, "Code Break", db, uuid)
        return {"status": "error"}

@router.post("/saveUserPreference")
async def save_user_preference(input: Theme):
    try:
        await profile_service.update_theme_db(input.user_name, input.theme)
    except:
        traceback.print_exc()
        db = await get_database(OEC_DEFAULT_CLIENT_ID)
        await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, input.session_id, input.user_name, "Code Break", db, input.uuid)
        return {"status": "error"}

@router.post("/removeConversation")
async def remove_conversation(input: removeConvoInterface):
    try:
        await remove_conversation_service.remove_convo(input.session_id, input.user_name)
        return {"status": "success"}
    except:
        traceback.print_exc()
        db = await get_database(OEC_DEFAULT_CLIENT_ID)
        await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, input.session_id, input.user_name, "Code Break", db, input.uuid)
        return {"status": "error"}
    
@router.post("/getUserPreference")
async def get_user_preference(input: GetTheme):
    try:
        preference = await profile_service.get_preference(input.user_name)
        return {"status": "success", "preference": preference}
    except:
        traceback.print_exc()
        db = await get_database(OEC_DEFAULT_CLIENT_ID)
        await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, input.session_id, input.user_name, "Code Break", db, input.uuid)
        return {"status": "error", "response": "Error while getting user preference..."}
    
@router.get('/getEnvVariables')
async def getEnvVariables():
    data = {

        "client_id": os.getenv('OIDC_CLIENT_ID'),
        "ohid_client_id": os.getenv('OHID_CLIENT_ID'),
        "authority": os.getenv('OIDC_AUTHORITY'),
        "ohid_authority": os.getenv('OPTUM_OHID_SSO_AUTHORITY'),
	    "client_secret": os.getenv('OIDC_CLIENT_SECRET'),
        "ohid_client_secret": os.getenv('OHID_SECRET'),
        "ohid_grant_type": os.getenv('OHID_GRANT_TYPE'),
        "redirect_uri": os.getenv('OIDC_REDIRECT_URI'),
        "ohid_redirect_uri" : os.getenv('OHID_REDIRECT_URI'),
        "acr_values": os.getenv('OIDC_ACR_VALUE'),
        "response_type": os.getenv('OIDC_RESPONSE_TYPE'),
        "scope": os.getenv('OIDC_SCOPE'),
        "automaticSilentRenew": os.getenv('OIDC_AUTOMATIC_SILENT_RENEW'),
        "msad_groups": os.getenv('OIDC_MSAD_GROUPS')
    }
    return JSONResponse(content=data)

@router.post("/transcribe")
async def transcribe_audio(file: UploadFile = File(...)):
    try:
        audio_buffer = io.BytesIO(await file.read())
        audio_buffer.name = file.filename
        transcription_result = await transcription_service.process_transcription(audio_buffer)
        return JSONResponse(
            content={"status": "success", "transcription": transcription_result},
            status_code=200
        )
    except ValueError as ve:
        traceback.print_exc()
        await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, "N/A", "N/A", "ValueError", await get_database(OEC_DEFAULT_CLIENT_ID))
        return JSONResponse(
            content={"status": "error", "message": "A value error occurred."},
            status_code=400
        )
    except Exception as e:
        traceback.print_exc()
        await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, "N/A", "N/A", "Exception", await get_database(OEC_DEFAULT_CLIENT_ID))
        return JSONResponse(
            content={"status": "error", "message": "An unexpected error occurred."},
            status_code=500
        )
  
@router.post("/surest-training-material-video")
async def surest_training_material_video_route(video_name: str = Form(...), video_url: str = Form(...), video_description: str = Form(...)):
    try:
        await create_or_update_surest_training_video(video_name, video_url, video_description)
        return JSONResponse(
            content={"status": "success"},
            status_code=200
        )
    except Exception as e:
        traceback.print_exc()
        await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, "N/A", "N/A", "ValueError", await get_database(OEC_DEFAULT_CLIENT_ID))
        return JSONResponse(
            content={"status": "error", "message": "An error occurred during the initialization of surest video"},
            status_code=500
        )
        
@router.post("/surest-training-material")
async def surest_training_material_document_route(file : UploadFile = File(...), file_name: str = Form(...), file_url: str = Form(...)):
    try:
        await create_or_update_surest_training_material(file, file_name, file_url)
        return JSONResponse(
            content={"status": "success"},
            status_code=200
        )
    except Exception as e:
        traceback.print_exc()
        await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, "N/A", "N/A", "ValueError", await get_database(OEC_DEFAULT_CLIENT_ID))
        return JSONResponse(
            content={"status": "error", "message": "An error occurred during the adding or updating of surest training material"},
            status_code=500
        )

@router.delete("/surest-training-material")
async def delete_surest_training_material_qa(file_name: str = Form(...), table_name: str = Form(...)):
    try:
        await delete_surest_training_material(table_name, file_name)
        return JSONResponse(
            content={"status": "success"},
            status_code=200
        )
    except Exception as e:
        traceback.print_exc()
        await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, "N/A", "N/A", "ValueError", await get_database(OEC_DEFAULT_CLIENT_ID))
        return JSONResponse(
            content={"status": "error", "message": "An error occurred during deleting the surest training material"},
            status_code=500
        )

@router.get("/surest-training-material")
async def get_surest_training_material(table_name: str):
    try:
        pdf_files = await get_all_surest_training_materials(table_name)
        return JSONResponse(
        content={"status": "success", "data": list(pdf_files)},
        status_code=200
        )  
    except Exception as e:
        traceback.print_exc()
        await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, "N/A", "N/A", "ValueError", await get_database(OEC_DEFAULT_CLIENT_ID))
        return JSONResponse(
            content={"status": "error", "message": "An error occurred during getting all the surest training material"},
            status_code=500
        )

@router.post("/agenticOfflineEvaluation")
async def agentic_offline_evaluation(
    query: str = Form(...),
    uuid: Optional[str] = Form(None),
    client_id: str = Form(...),
    user_name: str = Form(...),
    session_id: str = Form(...),
    agent_name: str = Form(...),
    application_name: str = Form(...),
    additional_arg: Optional[str] = Form(None)
):
    try:
        parsed_args = parse_additional_args(additional_arg, field_name="additional_arg")
    except json.JSONDecodeError:
        return JSONResponse(
            status_code=400,
            content={"status": "error", "message": "Invalid JSON format for additional_arg"}
        )
    payload = {
        "query": query,
        "uuid": uuid,
        "client_id": client_id,
        "user_name": user_name,
        "session_id": session_id,
        "agent_name": agent_name,
        "application_name": application_name,
        "additional_arg": parsed_args
    }

    is_valid_client = await validate_client_id(client_id)
    if not is_valid_client:
    # Return early with error message if client ID is not valid
        await logger.error(uuid, user_name, session_id, client_id, "api", "/generateResponse", payload, None, 403, "Client not registered on Lingo")
        return JSONResponse(
            status_code=403,
            content={"status": "success", "response": await map_final_response("The client / capability you are requesting is not registered on Lingo yet or not part of the current release. Please reach out to the Lingo Support for assistance.")}
        )
    
    else:
        try:
    # Call the service function
            raw_response = await offline_evaluation_service.trigger_agentic_offline_evaluation(query, session_id,  agent_name,user_name, application_name, parsed_args)

            mapped_response = await map_final_response(raw_response)

            # Return the mapped response
            return JSONResponse(
                content={
                    "status": "success",
                    "response": mapped_response
                },
                status_code=200
            )
    
        except Exception as e:
            traceback.print_exc()
            return JSONResponse(
                content={
                    "status": "error",
                    "message": "An unexpected error occurred while processing the request."
                },
                status_code=500
            )
    
@router.get('/fetchAllowedOrigin')  
async def fetch_allowed_origin():
    try:  
        get_allowed_origins = await get_allowed_origins_service.allowedOrigins()
        return get_allowed_origins 
    except Exception as e:  
        traceback.print_exc()  
        return JSONResponse(content={"status": "error", "message": "An unexpected error occurred."}, status_code=500) 
    
@router.post("/setupPostgresCheckpoints")
async def setup_postgres_checkpoints():
    try:
        postgres_checkpointer = await get_postgres_checkpointer()
        if not postgres_checkpointer:
            raise HTTPException(status_code=500, detail="Postgres checkpointer is not initialized")
        
        await postgres_checkpointer.setup()
        return JSONResponse(
            content={"status": "success", "message": "Postgres checkpoint setup completed successfully"},
            status_code=200
        )
        
    except Exception as e:
        traceback.print_exc()
        await log_error_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, "N/A", "N/A", "CheckpointSetupError", await get_database(OEC_DEFAULT_CLIENT_ID))
        return JSONResponse(
            content={"status": "error", "message": "An error occurred during postgres checkpoint setup"},
            status_code=500
        )
