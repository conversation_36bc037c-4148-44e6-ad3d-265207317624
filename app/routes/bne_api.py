from fastapi import APIRouter, File, UploadFile, Form
from fastapi.responses import JSONResponse
from client_plugins.bneportal.bneportal_utils.utils import (
    create_or_update_bne_training_material,
    delete_bne_training_material,
    get_all_bne_training_materials,
    create_or_update_bne_training_video
)
import traceback
from config.db import get_database
from services import log_error_service

router = APIRouter()

@router.post("/bne-training-material")
async def upload_bne_training_material_route(
    file: UploadFile = File(...),
    file_name: str = Form(...),
    file_url: str = Form(...),
    file_category: str = Form(...),
    user_type: str = Form(...),
    broker_training: str = Form(...),
    employer_training: str = Form(...),
    internal_training: str = Form(...),
):
    try:
        await create_or_update_bne_training_material(
            file, file_name, file_url, file_category, user_type, broker_training, employer_training, internal_training
        )
        return JSONResponse(
            content={"status": "success"},
            status_code=200,
        )
    except Exception as e:
        traceback.print_exc()
        await log_error_service.log_error_to_db(
            traceback.format_exc(), "OEC_DEFAULT_CLIENT_ID", "N/A", "N/A", "ValueError", await get_database("OEC_DEFAULT_CLIENT_ID")
        )
        return JSONResponse(
            content={"status": "error", "message": "An error occurred during the adding or updating of bne training material"},
            status_code=500,
        )

@router.delete("/bne-training-material")
async def delete_bne_training_material_route(file_name: str = Form(...), table_name: str = Form(...)):
    try:
        await delete_bne_training_material(table_name, file_name)
        return JSONResponse(
            content={"status": "success"},
            status_code=200,
        )
    except Exception as e:
        traceback.print_exc()
        await log_error_service.log_error_to_db(
            traceback.format_exc(), "OEC_DEFAULT_CLIENT_ID", "N/A", "N/A", "ValueError", await get_database("OEC_DEFAULT_CLIENT_ID")
        )
        return JSONResponse(
            content={"status": "error", "message": "An error occurred during deleting the bne training material"},
            status_code=500,
        )

@router.get("/bne-training-material")
async def get_bne_training_material_route(table_name: str):
    try:
        pdf_files = await get_all_bne_training_materials(table_name)
        return JSONResponse(
            content={"status": "success", "data": list(pdf_files)},
            status_code=200,
        )
    except Exception as e:
        traceback.print_exc()
        await log_error_service.log_error_to_db(
            traceback.format_exc(), "OEC_DEFAULT_CLIENT_ID", "N/A", "N/A", "ValueError", await get_database("OEC_DEFAULT_CLIENT_ID")
        )
        return JSONResponse(
            content={"status": "error", "message": "An error occurred during getting all the bne training material"},
            status_code=500,
        )
