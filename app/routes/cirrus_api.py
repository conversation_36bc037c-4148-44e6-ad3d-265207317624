from fastapi import APIRouter, Request
import traceback
from utils.schemas.cirrus_classes import *
from services.cirrus import get_contract_options_service, get_member_group_service, member_search_service, member_callback_service


router = APIRouter()

@router.post("/memberSearch")
async def member_search(input: CirrusMemberSearch):
    try:
        response = await member_search_service.member_search(input)
        return {"status": "success", "response" : response}
    except:
        traceback.print_exc()
        return {"status": "error", "response" : "some unexpected error ocurred"}


@router.post("/getContractOptions")
async def get_contract_options(input: ContractOptionsAndGroupSearchInterface):
    try:
       response = await get_contract_options_service.get_contract_options(input.memberGroupID, input.clientId, input.uuid)
       return {"status": "success", "response" : response}
    except:
       traceback.print_exc()
       return {"status" : "error", "response" : "some unexpected error ocurred"}


@router.post("/getMemberGroup")
async def get_member_group(input: ContractOptionsAndGroupSearchInterface):
    try:
        response = await get_member_group_service.get_member_group(input.memberGroupID, input.clientId, input.uuid)
        return {"status": "success", 'response' : response}
    except Exception as e:
        traceback.print_exc()
        return {"status":"error", "response": "some unexpected error ocurred"}
    
@router.post("/memberCallback/{caseId}/{uuid}/{clientId}")
async def member_callback(caseId, uuid, clientId, request: Request):
    try:
        callbackResp = await request.json()
        response = await member_callback_service.save_member_callback(caseId, uuid, clientId, callbackResp)
        return {"status" : "success", 'response' : response}
    except Exception as e:
        traceback.print_exc()
        return {"status" : "error", "response" : "some unexpected error ocurred"}
