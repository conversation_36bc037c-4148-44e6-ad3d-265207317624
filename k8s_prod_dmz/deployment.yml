apiVersion: apps/v1
kind: Deployment
metadata:
  name: lingo-api
  labels:
    app: lingo-api
    env: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lingo-api
  strategy: {}
  template:
    metadata:
      labels:
        app: lingo-api
    spec:
      containers:
        - name: lingo-api
          env:
            - name: VERIFY_SSL
              value: "False"
            - name: CIRRUS_URL_BASE
              value: https://gateway-dmz.optum.com/api/cel/cirrus
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: jwt-credentials
                  key: jwt-secret
            - name: JWT_KEY
              valueFrom:
                secretKeyRef:
                  name: jwt-credentials
                  key: jwt-key
            - name: AZURE_OPENAI_ENDPOINT_TOKEN
              value: "https://api.uhg.com/api/cloud/api-management/ai-gateway/1.0"
            - name: AZURE_OPENAI_ENDPOINT_KEY
              valueFrom:
                secretKeyRef:
                  name: azure-openai-credentials
                  key: endpoint-key
            - name: OPENAI_RESOURCE_FLAG
              value: "TOKEN"
            - name: AZURE_OPENAI_EMBEDDINGS_TOKEN
              value: "text-embedding-3-small_1"
            - name: AZURE_OPENAI_EMBEDDINGS_KEY
              value: "magnus-va-text-embedding-ada-002"
            - name: OPENAI_API_VERSION_TOKEN
              value: "2023-07-01-preview"
            - name: OPENAI_API_VERSION_KEY
              value: "2023-07-01-preview"
            - name: AZURE_OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: azure-openai-credentials
                  key: api-key
            - name: AZURE_OPENAI_CHAT_DEPLOYMENT_NAME_TOKEN
              value: "gpt-4o_2024-11-20"
            - name: AZURE_OPENAI_CHAT_DEPLOYMENT_NAME_KEY
              value: "gpt-4o"
            - name: AZURE_OPENAI_CHAT_MODEL_NAME_TOKEN
              value: "gpt-4o"
            - name: AZURE_OPENAI_CHAT_MODEL_NAME_KEY
              value: "gpt-4o"
            - name: OPENAI_PROJECT_ID
              value: "206a256d-cc09-453e-a364-28aa02c59ab7"
            - name: OPENAI_AUTH_URL
              value: "https://api.uhg.com/oauth2/token"
            - name: OPENAI_CLIENT_ID
              value: "926f99bf-5662-4547-9ddd-9fecb09836d5"
            - name: OPENAI_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: openai-credentials
                  key: client-secret
            - name: OPENAI_SCOPE
              value: "https://api.uhg.com/.default"
            - name: LINGO_OEC_BASE_URL
              value: "https://lingoai-api.optum.com/"
            - name: DB_START_STRING
              value: "Lingo"
            - name: DB_CORE
              value: "Lingo_Core"
            - name: MONGO_DB_URL
              valueFrom:
                secretKeyRef:
                  name: mongodb-credentials
                  key: mongodb-url
            - name: ENV
              value: "Prod"
            - name: TOKEN_SERVER_URL
              value: "https://authgateway1.entiam.uhg.com/as/token.oauth2"
            - name: SAMX_SERVICE_LAYER_URL
              value: "https://samxfi-uat-xport.hcck8s-ctc.optum.com/xport/api/ben/quick-quotes-sl/v1"
            - name: XTRACTOR_EMPLOYEES_API_URL
              value: "https://data-external-dev2-xtractor.hcck8s-ctc-np1.optum.com/app/gpt/get_employees"
            - name: XTRACTOR_GROUP_INFO_API_URL
              value: "https://data-external-dev2-xtractor.hcck8s-ctc-np1.optum.com/app/gpt/get_company_details"
            - name: XTRACTOR_RENEWAL_PLAN_API_URL
              value: "https://data-external-dev2-xtractor.hcck8s-ctc-np1.optum.com/app/gpt/get_plans"
            - name: XTRACTOR_SHOPPING_PLAN_API_URL
              value: "https://data-external-dev2-xtractor.hcck8s-ctc-np1.optum.com/app/gpt/get_allinfo"
            - name: MESSAGES_TO_DELETE_COUNT
              value: "20"
            - name: SUMMARY_TRIGGER_THRESHOLD
              value: "30"
            - name: NIMBUS_URL_BASE
              value: "https://gateway-dmz.optum.com/api/cust/cirrus"
            - name: MAGNUS_OPENAI_API_KEY
              value: "********************************"
            - name: MAGNUS_OPENAI_API_URL
              value: "https://r1vilefk6iopf8dopenai.openai.azure.com/"
            - name: MAGNUS_API_35_VERSION
              value: "2024-08-01-preview"
            - name: MAGNUS_API_4o_VERSION
              value: "2024-08-01-preview"
            - name: GPT_35_DEPLOYMENT
              value: "magnus-gpt-35-turbo-poc"
            - name: GPT_4o_DEPLOYMENT
              value: "magnus-gpt-4o-poc"
            - name: MAGNUS_EMBEDDING_VERSION
              value: "2023-05-15"
            - name: MAGNUS_EMBEDDING_DEPLOYMENT
              value: "magnus-embedding-ada002-poc"
            - name: MAGNUS_WHISPER_VERSION
              value: "2024-06-01"
            - name: MAGNUS_WHISPER_DEPLOYMENT
              value: "magnus-whisper"
            - name: DB_MEMBER_ENROLL
              value: "Lingo_Member_Enroll"
            - name: XTRACTOR_ENDPOINT
              value: "https://data-external-dev-xtractor.hcck8s-ctc-np1.optum.com/app/gpt/get_image_data"
            - name: EMAIL_AUTOMATION_ENDPOINT
              value: "https://acet-sal-api-pilot.optum.com/api/secure/documentExtraction/upload"
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: secret-access-key
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: access-key-id
            - name: AWS_FOLDER
              value: "prod"
            - name: AWS_BUCKET_NAME
              value: "bne-portal-form"
            - name: AWS_ENDPOINT
              value: "https://s3api-core.optum.com"
            - name: DB_XTRACTOR_DOCS
              value: "SBC_Extractor"
            - name: XTRACTOR_MONGO_DB_URL
              valueFrom:
                secretKeyRef:
                  name: xtractor-mongo-credentials
                  key: mongo-db-url
            - name: POSTGRES_HOST
              value: "rp000184942.uhc.com"
            - name: POSTGRES_USER
              value: "lingoai"
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-credentials
                  key: postgres-password
            - name: POSTGRES_DATABASE_NAME
              value: "lingoai"
            - name: OIDC_CLIENT_ID
              value: "Reg1_magnusOEC"
            - name: OHID_CLIENT_ID
              value: "1d62ac7a9fc8e9e656de5786b0c988b052b06871a1857f2a"
            - name: OIDC_AUTHORITY
              value: "https://authgateway1.entiam.uhg.com"
            - name: OPTUM_OHID_SSO_AUTHORITY
              value: "https://identity.onehealthcareid.com/oidc/authorize?"
            - name: OIDC_CLIENT_SECRET
              value: "ygY9skCTn&997ddF8j2qkRmyUWMXQSH45n6X6FTrTEY6"
            - name: OHID_SECRET
              valueFrom:
                secretKeyRef:
                  name: ohid-credentials
                  key: secret
            - name : OHID_JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: ohid-credentials
                  key: jwt-secret
            - name: OHID_GRANT_TYPE
              value: "authorization_code"
            - name: OIDC_REDIRECT_URI
              value: "https://lingo-ui.optum.com/"
            - name: OHID_REDIRECT_URI
              value: "https://lingo-ui.optum.com/"
            - name: OIDC_ACR_VALUE
              value: "R1_AAL1_MS-AD-Kerberos"
            - name: OIDC_RESPONSE_TYPE
              value: "code"
            - name: OIDC_SCOPE
              value: "openid profile address email phone"
            - name: OIDC_AUTOMATIC_SILENT_RENEW
              value: "true"
            - name: OIDC_MSAD_GROUPS
              value: "MagnusOEC_Users"
            - name : LANGFUSE_HOST
              value : "https://langfuse.hcck8s-ctc-np101.optum.com"
            - name : OPTUM_OHID_SSO_URL
              value : "https://identity.onehealthcareid.com/oidc/token"
            - name: SAMX_ONE_DB_URL
              valueFrom:
                secretKeyRef:
                  name: samx-one-db-credentials
                  key: db-url
            - name: SAMX_ONE_DB_NAME
              value: "SAMxOneDev"               
            - name: SAMX_COMM_CENTER_STARGATE_BASE_URL
              value: "" # TODO: Add correct production URL for production readiness.
            - name: "SAMX_COMM_CENTER_NB_URL"
              value: "" # TODO: Add correct production URL for production readiness.
            - name: PERMISSIONS_CACHE_TTL
              value: "3600" # 1 hour in seconds
          image: docker.repo1.uhc.com
          securityContext:
            runAsUser: 1001
            runAsGroup: 3000
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: false
            capabilities:
              drop:
                - KILL
                - MKNOD
                - SYS_CHROOT
          resources:
            requests:
              memory: 8G
              cpu: 1920m
            limits:
              memory: 8G
              cpu: 1920m
          readinessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 20
          volumeMounts:
            - mountPath: /app/email
              readOnly: false
              name: email
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 180
      volumes:
        - name: email
          persistentVolumeClaim:
            claimName: email
        - name: tls
          secret:
            secretName: lingo-api-cert
