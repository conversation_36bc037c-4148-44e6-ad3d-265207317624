apiVersion: apps/v1
kind: Deployment
metadata:
  name: magnus-oec-api-demo
  labels:
    app: magnus-oec-api-demo
    env: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: magnus-oec-api-demo
  strategy: {}
  template:
    metadata:
      labels:
        app: magnus-oec-api-demo
    spec:
      containers:
        - name: magnus-oec-api-demo
          env:
            - name: ENV_NAME
              value: demo
            - name: AZURE_OPENAI_CHAT_DEPLOYMENT_NAME_O
              value: gpt-4o_2024-05-13
            - name: VERIFY_SSL
              value: "False"
            - name: CIRRUS_URL_BASE
              value: https://gateway-stage-dmz.optum.com/api/master/cel/cirrus
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: jwt-credentials
                  key: jwt-secret
            - name: JWT_KEY
              valueFrom:
                secretKeyRef:
                  name: jwt-credentials
                  key: jwt-key
            - name: OPENAI_ENDPOINT
              value: "https://r1vilefk6iopf8dopenai.openai.azure.com/"
            - name: API_VERSION
              value: "2024-02-01"
            - name: AZURE_OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: azure-openai-credentials
                  key: api-key
            - name: AZURE_OPENAI_CHAT_DEPLOYMENT_NAME_TOKEN
              value: "gpt-4o_2024-05-13"
            - name: AZURE_OPENAI_CHAT_DEPLOYMENT_NAME_KEY
              value: "gpt_4o"
            - name: AZURE_OPENAI_CHAT_MODEL_NAME_TOKEN
              value: "gpt-4o"
            - name: AZURE_OPENAI_CHAT_MODEL_NAME_KEY
              value: "gpt-4o"
            - name: OPENAI_API_VERSION_TOKEN
              value: "2025-01-01-preview"
            - name: OPENAI_API_VERSION_KEY
              value: "2023-07-01-preview"
            - name: AZURE_OPENAI_ENDPOINT_TOKEN
              value: "https://api.uhg.com/api/cloud/api-management/ai-gateway/1.0"
            - name: AZURE_OPENAI_ENDPOINT_KEY
              valueFrom:
                secretKeyRef:
                  name: azure-openai-credentials
                  key: endpoint-key
            - name: OPENAI_PROJECT_ID
              value: "33de7400-191f-4e76-9e11-82a12254eced"
            - name: OPENAI_AUTH_URL
              value: "https://api.uhg.com/oauth2/token"
            - name: OPENAI_CLIENT_ID
              value: "4ced791b-d036-4783-9dd0-bba7fe759c10"
            - name: OPENAI_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: openai-credentials
                  key: client-secret
            - name: OPENAI_SCOPE
              value: "https://api.uhg.com/.default"
            - name: LINGO_OEC_BASE_URL
              value: "https://magnus-oec-api-demo.hcck8s-ctc-np101.optum.com/"
            - name: DB_START_STRING
              value: "Lingo"
            - name: ENV
              value: "demo"
            - name: MONGO_DB_URL
              valueFrom:
                secretKeyRef:
                  name: mongodb-credentials
                  key: mongodb-url
            - name: OIDC_MSAD_GROUPS
              value: "MagnusOEC_Users"
            - name: OIDC_AUTOMATIC_SILENT_RENEW
              value: "true"
            - name: OIDC_RESPONSE_TYPE
              value: "code"
            - name: OIDC_CLIENT_SECRET
              value: "WRYm6PGD7GbQD@P2k#o#N2eK4SJF$2xPEJzkda2Uv<om"
            - name: OIDC_SCOPE
              value: "openid profile address email phone"
            - name: OIDC_ACR_VALUE
              value: "R1_AAL1_MS-AD-Kerberos"
            - name: OIDC_REDIRECT_URI
              value: "https://magnus-oec-ui-demo.hcck8s-ctc-np101.optum.com/"
            - name: OHID_REDIRECT_URI
              value: "https://magnus-oec-ui-demo.hcck8s-ctc-np101.optum.com/"
            - name: OIDC_AUTHORITY
              value: "https://authgateway1-dev.entiam.uhg.com"
            - name: OIDC_CLIENT_ID
              value: "Reg1Dev_magnusOEC"
            - name: XTRACTOR_EMPLOYEES_API_URL
              value: "https://data-external-dev2-xtractor.hcck8s-ctc-np1.optum.com/app/gpt/get_employees"
            - name: XTRACTOR_GROUP_INFO_API_URL
              value: "https://data-external-dev2-xtractor.hcck8s-ctc-np1.optum.com/app/gpt/get_company_details"
            - name: XTRACTOR_RENEWAL_PLAN_API_URL
              value: "https://data-external-dev2-xtractor.hcck8s-ctc-np1.optum.com/app/gpt/get_plans"
            - name: XTRACTOR_SHOPPING_PLAN_API_URL
              value: "https://data-external-dev2-xtractor.hcck8s-ctc-np1.optum.com/app/gpt/get_allinfo"
            - name: MESSAGES_TO_DELETE_COUNT
              value: "20"
            - name: SUMMARY_TRIGGER_THRESHOLD
              value: "30"
            - name: NIMBUS_URL_BASE
              value: "https://gateway-stage-dmz.optum.com/api/master/cust/cirrus"
            - name: SAMX_SERVICE_LAYER_URL
              value: "https://samxfi-uat-xport.hcck8s-ctc.optum.com/xport/api/ben/quick-quotes-sl/v1"
            - name: TOKEN_SERVER_URL
              value: "https://authgateway1-dev.entiam.uhg.com/as/token.oauth2"
            - name: OPENAI_RESOURCE_FLAG
              value: "TOKEN"
            - name: AZURE_OPENAI_EMBEDDINGS_TOKEN
              value: "text-embedding-3-small_1"
            - name: AZURE_OPENAI_EMBEDDINGS_KEY
              value: "text-embedding-3-small"
            - name: DB_CORE
              value: "Lingo_Core"  
            - name : DB_MEMBER_ENROLL
              value : "Lingo_Member_Enroll"
            - name : XTRACTOR_ENDPOINT
              value : "https://data-external-dev-xtractor.hcck8s-ctc-np1.optum.com/app/gpt/get_image_data"
            - name : XTRACTOR_SBC_ENDPOINT
              value : "https://data-external-dev3-xtractor.hcck8s-ctc-np1.optum.com/app/sbc/get_sbc_data"
            - name : EMAIL_AUTOMATION_ENDPOINT
              value : "https://acet-sal-api-pilot-stage.optum.com/api/secure/documentExtraction/upload"
            - name : AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: secret-access-key
            - name : AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: access-key-id
            - name : AWS_FOLDER
              value : "stage"
            - name : AWS_BUCKET_NAME
              value : "bne-portal-form"
            - name : AWS_ENDPOINT
              value : "https://s3api-core.optum.com"
            - name : DB_XTRACTOR_DOCS
              value : "SBC_Extractor"
            - name : XTRACTOR_MONGO_DB_URL
              valueFrom:
                secretKeyRef:
                  name: xtractor-mongo-credentials
                  key: mongo-db-url
            - name : OHID_CLIENT_ID
              value : "MGNS0084532N"
            - name : OHID_SECRET   
              valueFrom:
                secretKeyRef:
                  name: ohid-credentials
                  key: secret
            - name : OHID_JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: ohid-credentials
                  key: jwt-secret
            - name : OHID_GRANT_TYPE
              value : "authorization_code"
            - name : OPTUM_OHID_SSO_URL
              value : "https://identity.nonprod.onehealthcareid.com/oidc/token"     
            - name : OPTUM_OHID_SSO_AUTHORITY
              value : "https://identity.nonprod.onehealthcareid.com/oidc/authorize?"
            - name : LANGFUSE_PUBLIC_KEY
              value : "pk-lf-b9b821b9-2d2a-4c37-87f0-115d9bd9d8ed"
            - name : LANGFUSE_HOST
              value : "https://langfuse.hcck8s-ctc-np101.optum.com"     
            - name: POSTGRES_HOST
              value: "rn000164202.uhc.com"
            - name: POSTGRES_USER
              value: "magnus_oec"
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-credentials
                  key: postgres-password
            - name: POSTGRES_DATABASE_NAME
              value: "magnus_oec"
            - name: SAMX_COMM_CENTER_STARGATE_BASE_URL
              value: https://gateway-stage-core.optum.com/api/gcpdev/ben/commcenter/v1
            - name: "SAMX_COMM_CENTER_NB_URL"
              value: "https://gateway-stage-dmz.optum.com/api/stage/ben/quick.quotes/v1/getnbcases"  
            - name: SAMX_ONE_DB_URL
              valueFrom:
                secretKeyRef:
                  name: samx-one-db-credentials
                  key: db-url
            - name: SAMX_ONE_DB_NAME
              value: "SAMxOneDev"                                                                                          
          image: docker.repo1.uhc.com
          securityContext:
            runAsUser: 1001
            runAsGroup: 3000
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: false
            capabilities:
              drop:
                - KILL
                - MKNOD
                - SYS_CHROOT
          resources:
            requests:
              memory: 8G
              cpu: 1920m
            limits:
              memory: 8G
              cpu: 1920m
          readinessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 20
          volumeMounts:
            - mountPath: /app/email
              readOnly: false
              name: email
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 180
      volumes:
        - name: email
          persistentVolumeClaim:
            claimName: email
        - name: tls
          secret:
            secretName: magnus-oec-api-demo-cert
