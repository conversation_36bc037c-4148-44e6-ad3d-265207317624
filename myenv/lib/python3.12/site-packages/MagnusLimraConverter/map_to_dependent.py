from .utils import format_dateSpaces, format_phone, format_ssnSpaces, process_pcp_pcd_fields
from .constants import STATES_LIST

def map_to_dependent(input_payload, employee_address, employee_address_confidence):
    # Mapping of input keys to dependent keys
    key_mappings = {
        "Last Name": "dependentLastName",
        "First Name": "dependentFirstName",
        "MI": "dependentMiddleName",
        "Sex": "dependentGenderCode",
        "Date of Birth": "dependentBirthDate",
        "Social Security Number": "dependentSocialSecurityNumber",
        "firstLineAddress": "dependentFirstLineAddress",
        "secondLineAddress": "dependentSecondLineAddress",
        "cityName": "dependentCityName",
        "stateProvinceCode": "dependentStateProvinceCode",
        "postalCode": "dependentPostalCode",
        "Existing Patient?": "existingPatient",
        "ID#": "dependentPcpID",
        "Name": "Name",
        "Address": "address",
        "Permanently disabled and age 26 or older": "permanentlyDisabledAndOver26",
        "Race/Ethnicity - Check all that apply": "raceEthnicity",
        "Primary Care Physician": "primaryCarePhysician",
        "Primary Care Dentist": "primaryCareDentist",
        "Do you use tobacco?": "dependentTobaccoUseCode",
        "If yes, are you currently participating in a tobacco cessation program or do you intend to join one?": "tobaccoCessationProgram"
    }

    # Mapping of dependent keys to labels
    label_mapping = {
        "dependentLastName": "Last Name",
        "dependentFirstName": "First Name",
        "dependentMiddleName": "Middle Initial",
        "dependentGenderCode": "Sex",
        "dependentBirthDate": "DOB",
        "dependentSocialSecurityNumber": "SSN",
        "dependentTobaccoUseCode": "Tobacco Use",
        "existingPatient": "Existing Patient?",
        "dependentPcpID": "PCP ID",
        "firstLineAddress": "Address",
        "secondLineAddress": "Apt Number",
        "cityName": "City",
        "stateProvinceCode": "State",
        "postalCode": "Postal Code",
        "dependentPcpID": "ID",
        "Name": "Name",
        "address": "Address",
        "tobaccoCessationProgram": "Tobacco Cessation Program",
        "permanentlyDisabledAndOver26": "Permanently Disabled And Over 26",
        "raceEthnicity": "Race/Ethnicity",
        "Primary Care Physician": "primaryCarePhysician",
        "Primary Care Dentist": "primaryCareDentist",
        "ID#": "ID",
        "Existing Patient?": "Existing Patient?"
    }

    # Process individual field
    def process_field(field):
        new_key = key_mappings.get(field["key"])
        value = field["value"]["checkbox"] if field["type"] == "single_select_checkbox" else field["value"]
        if value is None:
            value = ""
        score = round(field.get("confidence", 0) * 100, 1) if "confidence" in field else 0
        input_type = "Dropdown" if field["type"] == "single_select_checkbox" else field["type"]
        label = label_mapping.get(new_key)

        payload = {
            "fieldId": new_key,
            "fieldName": new_key,
            "inputType": input_type,
            "label": label,
            "score": score,
            "value": value
        }
        if field["type"] == "single_select_checkbox" and "checkboxOptions" in field:
            payload["options"] = field["checkboxOptions"]
        return payload

    # Check if all fields are blank
    def all_fields_blank(fields):
        for field in fields:
            if field.get("type") == "text" and field.get("value"):
                return False
        return True

    output_payload = []
    spouse_party_id = None
    dep_party_id = []
    member_counter = 2

    # Iterate through each section in the input payload
    for section in input_payload["fields"]:
        if all_fields_blank(section["fields"]):
            continue

        dependent_info = []
        address_if_different = None

        # Process each field in the section
        for field in section["fields"]:
            if "key" in field and key_mappings.get(field["key"]) is not None:
                if 'date' in field['key'].lower():
                    field["value"] = format_dateSpaces(field["value"])
                    field['type'] = "dateInput"
                if "phone" in field["key"].lower():
                    field["value"] = format_phone(field["value"])
                if "social security" in field["key"].lower():
                    field["value"] = format_ssnSpaces(field["value"])
                if field["key"] == "Address (if different from Employee)":
                    address_if_different = field["value"]

                dependent_info.append(process_field(field))

        # Process subsections for PCP and PCD fields
        for subsection in section["fields"]:
            if "section" in subsection:
                if subsection["section"] == "Primary Care Physician":
                    pcp_data = process_pcp_pcd_fields(subsection.get("fields", []), "PCP", True)
                    dependent_info.extend(pcp_data)
                elif subsection["section"] == "Primary Care Dentist":
                    pcd_data = process_pcp_pcd_fields(subsection.get("fields", []), "PCD", True)
                    dependent_info.extend(pcd_data)

        # Add dependent party ID
        dependent_info.append({
            "fieldId": "dependentPartyID",
            "fieldName": "dependentPartyID",
            "inputType": "text",
            "label": "Dependent Party ID",
            "score": 90,
            "value": member_counter
        })

        # Determine relationship type and increment member counter
        if "Dependent" in section["section"]:
            dep_party_id.append(member_counter)
        else:
            spouse_party_id = member_counter
        member_counter += 1

        # Add relationship type code
        dependent_info.append({
            "fieldId": "dependentRelationshipTypeCode",
            "fieldName": "dependentRelationshipTypeCode",
            "inputType": "text",
            "label": "Dependent Relationship Type",
            "score": 90,
            "value": "Dependent" if "Dependent" in section["section"] else "Spouse"
        })

        # Add address fields
        if not address_if_different:
            for address_key in ["firstLineAddress", "secondLineAddress", "cityName", "stateProvinceCode", "postalCode"]:
                field = {
                    "fieldId": key_mappings.get(address_key),
                    "fieldName": key_mappings.get(address_key),
                    "inputType": "Dropdown" if "state" in address_key else "text",
                    "label": label_mapping.get(address_key),
                    "score": employee_address_confidence.get("employee" + address_key[0].upper() + address_key[1:], 0),
                    "value": employee_address.get("employee" + address_key[0].upper() + address_key[1:], "")
                }
                if "state" in address_key:
                    field['options'] = STATES_LIST
                dependent_info.append(field)
        else:
            for address_key in ["firstLineAddress", "secondLineAddress", "cityName", "stateProvinceCode", "postalCode"]:
                field = {
                    "fieldId": key_mappings.get(address_key),
                    "fieldName": key_mappings.get(address_key),
                    "inputType": "Dropdown" if "state" in address_key else "text",
                    "label": label_mapping.get(address_key),
                    "score": 0,
                    "value": ""
                }
                if "state" in address_key:
                    field['options'] = STATES_LIST
                dependent_info.append(field)

        output_payload.append(dependent_info)

    return output_payload, spouse_party_id, dep_party_id