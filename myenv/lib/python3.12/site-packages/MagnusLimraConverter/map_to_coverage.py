import copy  
  
def map_to_coverage(input_payload, employee_id, spouse_id, dep_id):  
    output_payload = []  
  
    # Define additional fields structure (excluding unwanted fields)  
    additional_fields = {  
        "productTypeCode": {  
            "fieldId": "productTypeCode",  
            "fieldName": "productTypeCode",  
            "inputType": "text",  
            "label": "Product Type Code", 
            "score": 100,  
            "value": "",  
        },  
        "planInfo": {  
            "fieldId": "planName",  
            "fieldName": "planName",  
            "inputType": "Dropdown",  
            "label": "Plan Name",  
            "options": [],  
            "planList": [],  
            "score": None,
            "value": "",  
        },  
        "insuredIds": {  
            "fieldId": "insuredPartyID",  
            "fieldName": "insuredPartyID",  
            "inputType": "text",  
            "label": "Insured Party ID",  
            "score": None,
            "value": [],  
        }  
    }  
  
    # Dictionary to group coverages by product type (column)  
    grouped_coverages = {}  
  
    # Iterate through each field in the input JSON  
    for field in input_payload['fields']:  
        if field['type'] == 'table_with_checkbox':  
            # Process each cell in the table  
            for cell in field['value']:  
                row = cell['row']  
                column = cell['column']  
                checkbox_value = cell['value']['checkbox']  
                plan_text = cell['value']['text'].strip()  
                confidence = cell['confidence']  # Extract confidence value  
  
                if checkbox_value == "Yes":  
                    # If the product type (column) is not yet in grouped_coverages, initialize it  
                    if column not in grouped_coverages:  
                        # Initialize a coverage object  
                        coverage = []  
  
                        # Add product type  
                        product_type = copy.deepcopy(additional_fields["productTypeCode"])  
                        product_type["value"] = column  
                        coverage.append(product_type)  
  
                        # Add plan name  
                        plan_info = copy.deepcopy(additional_fields["planInfo"])  
                        plan_info["value"] = plan_text  
                        plan_info["score"] = int(confidence * 100)
                        coverage.append(plan_info)  
  
                        # Add insured party IDs  
                        insured_ids = copy.deepcopy(additional_fields["insuredIds"])  
                        coverage.append(insured_ids)  
  
                        grouped_coverages[column] = coverage  
  
                    # Append insured party IDs to the existing coverage  
                    insured_ids = grouped_coverages[column][2]  # The insuredIds field is the third item in the coverage list  
                    if row == "Employee":  
                        insured_ids["value"].append(employee_id)  
                    elif row == "Spouse/Domestic Partner" and spouse_id is not None:  
                        insured_ids["value"].append(spouse_id)  
                    elif row == "Dependent":  
                        insured_ids["value"].extend(dep_id)  
  
    # Convert grouped_coverages back to a list for output_payload  
    output_payload = list(grouped_coverages.values())  
  
    return output_payload  