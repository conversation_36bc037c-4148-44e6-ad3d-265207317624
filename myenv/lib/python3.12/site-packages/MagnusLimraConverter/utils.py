import re

def format_dateSpaces(date_string):  
    if date_string:     
        date_list = date_string.replace(" ", "")
        return date_list
    return date_string  
    
def change_date_format(date_string):
    try:  
        if date_string:     
            # Replace empty space and hyphen with forward slash and split the date string
            date_list = date_string.replace(" ", "").replace("-","/").split("/")   
            
            # check if the input date is in MM/DD/YYYY format
            if len(date_list) == 3:  
                if len(date_list[0]) == 2:  
                    year = date_list[2]  
                    month = date_list[0]  
                    day = date_list[1]  
                
                # if not, assume it's in YYYY/MM/DD format  
                else:  
                    year = date_list[0]  
                    month = date_list[1]  
                    day = date_list[2]  
                
                # combine the year, month, and day in the desired format  
                new_date_string = f"{month}/{day}/{year}"  
            
                return new_date_string
        return date_string
    except Exception as e:
        return date_string

# Format date for cirrus submission
def change_date_format_cirrus(date_string):  
    if date_string:     
        date_list = date_string.replace(" ", "").split("/")   
        
        # check if the input date is in MM/DD/YYYY format
        if len(date_list) == 3:  
            if len(date_list[0]) == 2:  
                year = date_list[2]  
                month = date_list[0]  
                day = date_list[1]  
            
            # if not, assume it's in YYYY/MM/DD format  
            else:  
                year = date_list[0]  
                month = date_list[1]  
                day = date_list[2]  
            
            # combine the year, month, and day in the desired format  
            new_date_string = f"{month}-{day}-{year}"  
        
            return new_date_string
    return date_string  

def format_phoneSpaces(phone): 
    if phone: 
        #remove spaces and curly braces from phone number 
        phone = phone.replace(' ', '')
    return phone
        

def format_phone(phone): 
    if phone: 
        #remove spaces, parentheses, and hyphens from phone number 
        phone = phone.replace('(', '').replace(')', '').replace(' ', '').replace('-', '')  
        
        # Add parentheses and hyphen at the appropriate positions
        formatted_phone = '(' + phone[:3]
        if len(phone) > 2:
            formatted_phone += ')'
        formatted_phone += phone[3:6]
        if len(phone) > 5:
            formatted_phone += '-'
        formatted_phone += phone[6:]
        
        return formatted_phone
    return phone
        
def format_ssnSpaces(ssn):
    if ssn and len(ssn) >= 9:
        # Remove spaces and hyphens from SSN  
        ssn = ssn.replace(' ', '')
        return ssn  
    return ssn

def format_ssn(ssn):
    if ssn and len(ssn) >= 9:
        # Remove spaces from SSN, but keep hyphens
        ssn = ssn.replace(' ', '')  
        ssn = re.sub(r'\D', '', ssn)
        formatted_ssn = ssn[:3] + '-' + ssn[3:5] + '-' + ssn[5:]
        # Remove double hyphens
        formatted_ssn = formatted_ssn.replace('--', '-')
        return formatted_ssn
    return ssn

def format_salary(salary):
    if salary:
        # Remove dollar signs from salary  
        salary = salary.replace('$', '')  
        return salary
    return salary  

def unformat_phone_number(phoneString):
    if phoneString:
        # Remove hyphens from phone string, also remove any spaces and parentheses
        return phoneString.replace('-', '').replace(' ', '').replace('(', '').replace(')', '')
    return phoneString

def unformat_ssn(ssn):
    if ssn and len(ssn) >= 9:
        # Remove spaces and hyphens from SSN  
        ssn = ssn.replace(' ', '').replace('-', '')  
        #return in format XXXXXXXXX 
        return ssn  
    return ssn


def process_pcp_pcd_fields(fields, prefix, is_dependent=False):
    pcp_pcd_data = []
    key_mapping = {
        "Existing Patient?": f"{prefix}existingPatient",
        "Name": f"{prefix}Name",
        "Address": f"{prefix}address",
        "ID#": f"{prefix}{'dependent' if is_dependent else 'employee'}{'Pcp' if prefix == 'PCP' else 'Pcd'}ID"
    }
    label_mapping = {
        "Existing Patient?": f"{prefix} Existing Patient?",
        "Name": f"{prefix} Name",
        "Address": f"{prefix} Address",
        "ID#": f"{prefix} ID"
    }
    for field in fields:
        new_key = key_mapping.get(field.get("key"))
        if new_key is None:
            continue
        field_data = {
            "fieldId": new_key,
            "fieldName": new_key,
            "inputType": "text",
            "label": label_mapping.get(field.get("key")),
            "score": round(field.get("confidence", 0) * 100, 1),
            "value": field.get("value", "")
        }
        if field.get("type") in ["single_select_checkbox", "multi_select_checkbox"]:
            field_data["inputType"] = "Dropdown"
            field_data["options"] = field.get("checkboxOptions", [])
            if field["type"] == "single_select_checkbox":
                field_data["value"] = field["value"]["checkbox"]
                field_data["score"] = round(field["value"]["confidence"] * 100, 1)
            else:
                selected_option = next((item for item in field["value"] if item["checkbox"]), None)
                if selected_option:
                    field_data["value"] = selected_option["checkbox"]
                    field_data["score"] = round(selected_option["confidence"] * 100, 1)
                else:
                    field_data["value"] = ""
                    field_data["score"] = 0
        pcp_pcd_data.append(field_data)
    return pcp_pcd_data