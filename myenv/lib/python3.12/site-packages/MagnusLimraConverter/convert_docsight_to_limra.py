from enum import Enum
from .map_to_employer import map_to_employer
from .map_to_employee import map_to_employee
from .map_to_prior_medical_insurance_info import map_to_prior_medical_insurance_info
from .map_to_other_medical_coverage import map_to_other_medical_coverage
from .map_to_dependent import map_to_dependent
from .map_to_coverage import map_to_coverage

class SectionName(Enum):
  EMPLOYER = "To Be Completed By Employer"
  EMPLOYEE = "Employee Information"
  PRIOR_MEDICAL = "Prior Medical Insurance Information"
  OTHER_MEDICAL = "Other Medical Coverage Information"
  FAMILY = "Family Information"
  PRODUCT_SELECTION = "Product Selection"

def convert_docsight_to_limra(docsight):  
    limra_output = {"extractedForms": []}  
    for payload_item in docsight.get("payload", []):  
        for form in payload_item.get("extractedForms", []):  
            limra_form = {}  
            employee_address = {}  
            employee_address_confidence = {}  
            employee_id = None  
            spouse_id = None  
            dependent_ids = []
  
            for section in form.get("extractedFields", []):  
                section_name = section.get("section")  
  
                if section_name == SectionName.EMPLOYER.value:  
                    employer_data, employee_info = map_to_employer(section.get("fields", []))  
                    limra_form["employer"] = employer_data  
  
                elif section_name == SectionName.EMPLOYEE.value:  
                    employee_data, employee_address, employee_address_confidence, employee_id = map_to_employee(section)  
                    limra_form["employee"] = employee_data + employee_info  
  
                elif section_name == SectionName.FAMILY.value:  
                    family_data, spouse_id, dependent_ids = map_to_dependent(  
                        section, employee_address, employee_address_confidence  
                    )  

                    limra_form["dependents"] = family_data  
  
                elif section_name == SectionName.PRODUCT_SELECTION.value:  
                    coverage_data = map_to_coverage(section, employee_id, spouse_id, dependent_ids)  
                    limra_form["coverage"] = coverage_data  
  
                elif section_name == SectionName.PRIOR_MEDICAL.value:  
                    prior_medical_info = map_to_prior_medical_insurance_info(section.get("fields", []))  
                    limra_form["priorMedicalInsuranceInfo"] = prior_medical_info  
  
                elif section_name == SectionName.OTHER_MEDICAL.value:  
                    other_medical_coverage_info = map_to_other_medical_coverage(section.get("fields", []))  
                    limra_form["medicareEmployeeInfo"] = other_medical_coverage_info["medicareEmployeeInfo"]  
                    limra_form["medicareSpouseDependentInfo"] = other_medical_coverage_info["medicareSpouseDependentInfo"]  
                    limra_form["otherMedicalCoverageInfo"] = other_medical_coverage_info["otherMedicalCoverageInfo"]  
  
            limra_output["extractedForms"].append(limra_form)  
    return limra_output  
