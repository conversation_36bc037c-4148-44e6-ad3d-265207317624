from .constants import STATES_LIST
from .utils import format_dateSpaces, format_phone, format_ssnSpaces , process_pcp_pcd_fields

def map_to_employee(input_data):
    # Key and label mappings
    key_mapping = {
        "Last Name": "employeeLastName",
        "First Name": "employeeFirstName",
        "MI": "employeeMiddleName",
        "Social Security Number": "employeeSocialSecurityNumber",
        "Address": "employeeFirstLineAddress",
        "Apt #": "employeeSecondLineAddress",
        "City": "employeeCityName",
        "State": "employeeStateProvinceCode",
        "ZIP Code": "employeePostalCode",
        "Home Phone": "employeeHomePhone",
        "Cell Phone": "employeeMobilePhone",
        "Work Phone": "employeeWorkPhone",
        "Date of Birth": "employeeBirthDate",
        "Sex": "employeeGenderCode",
        "Marital status": "maritalStatusCode",
        "Preferred Language": "employeePreferredLanguage",
        "Email Address": "employeeEmail",
        "Do you use tobacco?": "employeeTobaccoUseCode",
        "Check here to receive your required plan communications by mail": "employeePlanCommunicationsByMail",  
        "If yes, are you currently participating in a tobacco cessation program or do you intend to join one?": "tobaccoCessationProgram",
        "Race/Ethnicity - Check all that apply": "raceEthnicity",
    }
    label_mapping = {
        "Last Name": "Last Name",
        "First Name": "First Name",
        "MI": "Middle Initial",
        "Social Security Number": "SSN",
        "Address": "Address",
        "Apt #": "Apt Number",
        "City": "City",
        "State": "State",
        "ZIP Code": "Postal Code",
        "Home Phone": "Home Phone",
        "Cell Phone": "Mobile Phone",
        "Work Phone": "Work Phone",
        "Date of Birth": "DOB",
        "Sex": "Sex",
        "Marital status": "Marital Status",
        "Preferred Language": "Preferred Language",
        "Check here to receive your required plan communications by mail": "Plan Communications By Mail", 
        "Email Address": "Email Address",
        "Do you use tobacco?": "Tobacco Use",
        "If yes, are you currently participating in a tobacco cessation program or do you intend to join one?": "Tobacco Cessation Program",
        "Race/Ethnicity - Check all that apply": "Race/Ethnicity",
    }

    employee = []
    employee_address = {}
    employee_address_confidence = {}

    # Process each field in the input data
    for field in input_data.get("fields", []):
        if "section" in field:
            # Handle sections for Primary Care Physician and Primary Care Dentist
            if field["section"] == "Primary Care Physician":
                pcp_data = process_pcp_pcd_fields(field.get("fields", []), "PCP")
                employee.extend(pcp_data)
            elif field["section"] == "Primary Care Dentist":
                pcd_data = process_pcp_pcd_fields(field.get("fields", []), "PCD")
                employee.extend(pcd_data)
            else:
                process_field(field, key_mapping, label_mapping, employee)
        else:
            # Apply formatting functions for specific fields
            if field.get("key") == "Social Security Number":
                field["value"] = format_ssnSpaces(field.get("value"))
            elif "Phone" in field.get("key"):
                field["value"] = format_phone(field.get("value"))
            elif "Date of Birth" in field.get("key"):
                field["value"] = format_dateSpaces(field.get("value"))

            process_field(field, key_mapping, label_mapping, employee)

        # Handle address-related fields
        if field.get("key") in ["Address", "Apt #", "City", "State", "ZIP Code"]:
            address_key = key_mapping[field["key"]]
            employee_address[address_key] = field.get("value", "")
            employee_address_confidence[address_key] = round(field.get("confidence", 0) * 100, 1)

    # Add employeePartyID field
    employee.append({
        "fieldId": "employeePartyID",
        "fieldName": "employeePartyID",
        "inputType": "text",
        "label": "Member ID",
        "score": None,
        "value": "1"
    })

    return employee, employee_address, employee_address_confidence, 1

def process_field(field, key_mapping, label_mapping, employee):
    # Map field key to new key
    new_key = key_mapping.get(field.get("key"))
    if new_key is None:
        return
    # Create field data dictionary
    field_data = {
        "fieldId": new_key,
        "fieldName": new_key,
        "inputType": "text",
        "label": label_mapping.get(field.get("key")),
        "score": round(field.get("confidence", 0) * 100, 1),
        "value": field.get("value", "")
    }
    # Handle checkbox fields
    if field.get("type") in ["single_select_checkbox", "multi_select_checkbox"]:
        field_data["inputType"] = "Dropdown"
        field_data["options"] = field.get("checkboxOptions", [])
        if field["type"] == "single_select_checkbox":
            field_data["value"] = field["value"]["checkbox"] if field["value"]["checkbox"] is not None else ""
            field_data["score"] = round(field["value"]["confidence"] * 100, 1)
        else:
            selected_option = next((item for item in field["value"] if item["checkbox"]), None)
            if selected_option:
                field_data["value"] = selected_option["checkbox"]
                field_data["score"] = round(selected_option["confidence"] * 100, 1)
            else:
                field_data["value"] = ""
                field_data["score"] = 0
    # Handle date fields
    if 'date' in field.get("key", "").lower():
        field_data["inputType"] = "dateInput"
    # Handle state fields
    if field.get("key") == "State":
        field_data["inputType"] = "Dropdown"
        field_data["options"] = STATES_LIST
    # Append field data to employee list
    employee.append(field_data)