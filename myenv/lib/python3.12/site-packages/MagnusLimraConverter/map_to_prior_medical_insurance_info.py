def map_to_prior_medical_insurance_info(fields):  
    key_mapping = {  
        "Within the last 12 months, have you, your spouse, or your dependents had any other medical coverage?": "otherMedicalCoverage",  
        "Prior medical carrier name": "priorMedicalCarrierName",  
        "Effective date": "priorMedicaleffectiveDate",  
        "End date": "priorMedicalendDate",  
        "Prior coverage type:": "priorCoverageType",  
    }  
  
    label_mapping = {  
        "Within the last 12 months, have you, your spouse, or your dependents had any other medical coverage?": "Other Medical Coverage",  
        "Prior medical carrier name": "Prior medical carrier name",  
        "Effective date": "Effective date",  
        "End date": "End date",  
        "Prior coverage type:": "Prior coverage type:",  
    }  
  
    input_type_mapping = {  
        "Within the last 12 months, have you, your spouse, or your dependents had any other medical coverage?": "Dropdown",  
        "Prior coverage type:": "Dropdown",  
    }  
  
    prior_medical_info = []  
  
    for field in fields:  
        new_key = key_mapping.get(field.get("key"))  
        if new_key is None:  
            continue  
  
        field_data = {  
            "fieldId": new_key,  
            "fieldName": new_key,  
            "inputType": input_type_mapping.get(field.get("key"), "text"),  
            "label": label_mapping.get(field.get("key")),  
            "score": round(field.get("confidence", 0) * 100, 1) if field.get("confidence") else 0,  
            "value": field.get("value", "") if field.get("value") is not None else "",  
        }  
  
        # Handle single and multi-select checkboxes  
        if field.get("type") in ["single_select_checkbox", "multi_select_checkbox"]:  
            field_data["inputType"] = "Dropdown"  
            field_data["options"] = field.get("checkboxOptions", [])  
  
            # For single-select checkboxes  
            if field["type"] == "single_select_checkbox":  
                field_data["value"] = field["value"].get("checkbox", "") if field["value"] and field["value"].get("checkbox") is not None else ""  
  
            # For multi-select checkboxes  
            elif field["type"] == "multi_select_checkbox" and isinstance(field.get("value"), list):  
                # Collect all selected checkboxes into a list  
                field_data["value"] = [  
                    checkbox_item.get("checkbox", "")  
                    for checkbox_item in field["value"]  
                    if checkbox_item.get("checkbox")  
                ]  
                if not field_data["value"]:  # If the list is empty, ensure it is explicitly set as an empty list  
                    field_data["value"] = []  
                # Set the score based on the first checkbox's confidence, if available  
                if field["value"] and len(field["value"]) > 0:  
                    field_data["score"] = round(field["value"][0].get("confidence", 0) * 100, 1)  
  
        prior_medical_info.append(field_data)  
  
    return prior_medical_info