def map_to_employer(fields):  
    employer_key_mapping = {  
        "Group Name": "employerName",  
        "Group Number": "employerPartyID"  
    }  
  
    employer_label_mapping = {  
        "Group Name": "Group Name",  
        "Group Number": "Group Number"  
    }  
  
    employee_key_mapping = {  
        "Requested Effective Date of Coverage/Date of Change": "effectiveDate",  
        "Date of Hire": "employeeOriginalHireDate",  
        "Hours Worked per week": "employeeWorkHoursQuantity",  
        "Salary": "employeeIncomeAmount",  
        "Reason for Application": "employeeEventTypeReasonCode",  
        "Employee Type": "employeeEmploymentStatus",
        "Position/Title": "employeePositionTitle"
    }  
  
    employee_label_mapping = {  
        "Requested Effective Date of Coverage/Date of Change": "Effective Date",  
        "Date of Hire": "Date of Hire",  
        "Hours Worked per week": "Hours Worked per Week",  
        "Salary": "Salary",  
        "Reason for Application": "Reason for Application",  
        "Employee Type": "Employee Type",
        "Position/Title": "Position/Title",
    }  
  
    employee_input_type_mapping = {  
        "Requested Effective Date of Coverage/Date of Change": "dateInput",  
        "Date of Hire": "dateInput",  
        "Hours Worked per week": "text",  
        "Salary": "text",  
        "Reason for Application": "Dropdown",  
        "Employee Type": "Dropdown",
        "Position/Title": "test"
    }  
  
    employer = []  
    employeeInfo = []  
  
    for field in fields:  
        employer_key = employer_key_mapping.get(field.get("key"))  
        employee_key = employee_key_mapping.get(field.get("key"))  
  
        if employer_key:  
            field_data = {  
                "fieldId": employer_key,  
                "fieldName": employer_key,  
                "inputType": "text",  
                "label": employer_label_mapping.get(field.get("key")),  
                "score": round(field.get("confidence", 0) * 100, 1),  
                "value": field.get("value", "")  
            }  
            employer.append(field_data)  
  
        if employee_key:  
            field_data = {  
                "fieldId": employee_key,  
                "fieldName": employee_key,  
                "inputType": employee_input_type_mapping.get(field.get("key"), "text"),  
                "label": employee_label_mapping.get(field.get("key")),  
                "score": round(field.get("confidence", 0) * 100, 1) if field.get("confidence") else "",  
                "value": field.get("value", "")  
            }  
            # Conditionally add options only if applicable  
            if employee_key in ["employeeEventTypeReasonCode", "employeeEmploymentStatus"]:  
                options = field.get("checkboxOptions", [])  
                field_data["options"] = options  
                # Extract the value from the list of dictionaries  
                if isinstance(field.get("value"), list) and field.get("value"):  
                    # Collect all selected checkboxes into a list  
                    field_data["value"] = [  
                        checkbox_item.get("checkbox", "")  
                        for checkbox_item in field.get("value")  
                        if checkbox_item.get("checkbox")  
                    ]  
                    # Use the score of the first index  
                    field_data["score"] = round(field["value"][0].get("confidence", 0) * 100, 1)  
  
            employeeInfo.append(field_data)  
  
    return employer, employeeInfo