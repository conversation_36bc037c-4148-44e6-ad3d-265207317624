def map_to_other_medical_coverage(input_data):  
    # Initialize the output dictionary  
    output_data = {  
        "medicareEmployeeInfo": [],  
        "medicareSpouseDependentInfo": [],  
        "otherMedicalCoverageInfo": [],  
    }  
  
    # Define key mappings for other medical coverage info  
    other_medical_key_mapping = {  
        "On the day this coverage begins, will you, your spouse or any of your dependents be covered under any other medical health plan or policy, including another UnitedHealthcare plan or Medicare?": "otherMedicalCoverage",  
        "Name of other carrier": "nameOfOtherCarrier"  
    }  
  
    # Define key mappings for Medicare - Employee Information  
    medicare_employee_key_mapping = {  
        "Name": "medicareEmployeeName",  
        "Medicare ID#": "employeeMedicareId",  
        "Part A enrollment": "employeePartAEnrollment",  
        "Part B enrollment": "employeePartBEnrollment",  
        "Part D enrollment": "employeePartDEnrollment",  
        "Reason for Medicare eligibility": "reasonForEmployeeMedicareEligibility",  
        "Are you receiving Social Security Disability Insurance (SSDI)?": "receivingSSDI"  
    }  
  
    # Define key mappings for Medicare - Spouse/Dependent  
    medicare_spouse_dependent_key_mapping = {  
        "Name": "medicareSpouseDependentName",  
        "Medicare ID#": "spouseDependentMedicareId",  
        "Part A enrollment": "spouseDependentPartAEnrollment",  
        "Part B enrollment": "spouseDependentPartBEnrollment",  
        "Part D enrollment": "spouseDependentPartDEnrollment",  
        "Reason for Medicare eligibility": "reasonForSpouseDependentMedicareEligibility"  
    }  
  
    # Define key mappings for table fields  
    table_key_mapping = {  
        "EmployeeName": "employeeName",  
        "EmployeeEffectiveDateMM/DD/YY": "employeeEffectiveDate",  
        "EmployeeEndDateMM/DD/YY": "employeeEndDate",  
        "EmployeeNameanddateofbirthofpolicyholderforothercoverage": "employeeNameAndDOB",  
        "Spouse/DomesticPartnerName": "spouseDomesticPartnerName",  
        "Spouse/DomesticPartnerEffectiveDateMM/DD/YY": "spouseDomesticPartnerEffectiveDate",  
        "Spouse/DomesticPartnerEndDateMM/DD/YY": "spouseDomesticPartnerEndDate",  
        "Spouse/DomesticPartnerNameanddateofbirthofpolicyholderforothercoverage": "spouseDomesticPartnerNameAndDOB",  
        "Dependent1Name": "dependent1Name",  
        "Dependent1Type(B/S/F)*": "dependent1TypeBSF",  
        "Dependent1EffectiveDateMM/DD/YY": "dependent1EffectiveDate",  
        "Dependent1EndDateMM/DD/YY": "dependent1EndDate",  
        "Dependent1Nameanddateofbirthofpolicyholderforothercoverage": "dependent1NameAndDOB",  
        "Dependent2Name": "dependent2Name",  
        "Dependent2Type(B/S/F)*": "dependent2TypeBSF",  
        "Dependent2EffectiveDateMM/DD/YY": "dependent2EffectiveDate",  
        "Dependent2EndDateMM/DD/YY": "dependent2EndDate",  
        "Dependent2Nameanddateofbirthofpolicyholderforothercoverage": "dependent2NameAndDOB",  
        "Dependent3Name": "dependent3Name",  
        "Dependent3Type(B/S/F)*": "dependent3TypeBSF",  
        "Dependent3EffectiveDateMM/DD/YY": "dependent3EffectiveDate",  
        "Dependent3EndDateMM/DD/YY": "dependent3EndDate",  
        "Dependent3Nameanddateofbirthofpolicyholderforothercoverage": "dependent3NameAndDOB"  
    }  
  
    # Define label mappings for table fields  
    table_label_mapping = {  
        "EmployeeName": "Employee Name",  
        "EmployeeEffectiveDateMM/DD/YY": "Employee Effective Date",  
        "EmployeeEndDateMM/DD/YY": "Employee End Date",  
        "EmployeeNameanddateofbirthofpolicyholderforothercoverage": "Employee Name And DOB",  
        "Spouse/DomesticPartnerName": "Spouse/Domestic Partner Name",  
        "Spouse/DomesticPartnerEffectiveDateMM/DD/YY": "Spouse/Domestic Partner Effective Date",  
        "Spouse/DomesticPartnerEndDateMM/DD/YY": "Spouse/Domestic Partner End Date",  
        "Spouse/DomesticPartnerNameanddateofbirthofpolicyholderforothercoverage": "Spouse/Domestic Partner Name And DOB",  
        "Dependent1Name": "Dependent 1 Name",  
        "Dependent1Type(B/S/F)*": "Dependent 1 Type BSF",  
        "Dependent1EffectiveDateMM/DD/YY": "Dependent 1 Effective Date",  
        "Dependent1EndDateMM/DD/YY": "Dependent 1 End Date",  
        "Dependent1Nameanddateofbirthofpolicyholderforothercoverage": "Dependent 1 Name And DOB",  
        "Dependent2Name": "Dependent 2 Name",  
        "Dependent2Type(B/S/F)*": "Dependent 2 Type BSF",  
        "Dependent2EffectiveDateMM/DD/YY": "Dependent 2 Effective Date",  
        "Dependent2EndDateMM/DD/YY": "Dependent 2 End Date",  
        "Dependent2Nameanddateofbirthofpolicyholderforothercoverage": "Dependent 2 Name And DOB",  
        "Dependent3Name": "Dependent 3 Name",  
        "Dependent3Type(B/S/F)*": "Dependent 3 Type BSF",  
        "Dependent3EffectiveDateMM/DD/YY": "Dependent 3 Effective Date",  
        "Dependent3EndDateMM/DD/YY": "Dependent 3 End Date",  
        "Dependent3Nameanddateofbirthofpolicyholderforothercoverage": "Dependent 3 Name And DOB"  
    }  
  
    # Helper function to process a field  
    def process_field(field, key_mapping, section_key, label_mapping=None):  
        field_key = field.get("key")  
        new_key = key_mapping[field_key]  
        label = label_mapping.get(field_key, field_key) if label_mapping else field_key  
    
        # Determine the value based on field type  
        if field["type"] == "single_select_checkbox":  
            # Handle single_select_checkbox fields specifically  
            value = field["value"]["checkbox"] if field["value"]["checkbox"] is not None else ""  
        else:  
            # Default handling for other field types  
            value = field.get("value", "")  
    
        # Construct the field data object  
        field_data = {  
            "fieldId": new_key,  
            "fieldName": new_key,  
            "inputType": "Dropdown" if field["type"] == "single_select_checkbox" else "text",  
            "label": label,  
            "score": round(field.get("confidence", 0) * 100, 1),  
            "value": value,  
        }  
    
        # Add checkbox options if the field type is single_select_checkbox  
        if field["type"] == "single_select_checkbox":  
            field_data["options"] = field.get("checkboxOptions", [])  
    
        # Append the processed field data to the appropriate section in output_data  
        output_data[section_key].append(field_data)  
  
    # Process each field in the input data  
    for field in input_data:  
        if field.get("key") in other_medical_key_mapping:  
            process_field(field, other_medical_key_mapping, "otherMedicalCoverageInfo")  
        elif field.get("type") == "table":  
            for cell in field.get("value", []):  
                row_key = cell["row"].replace(' ', '')  
                column_key = cell["column"].replace(' ', '')  
                new_key = f"{row_key}{column_key}"  
                mapped_key = table_key_mapping.get(new_key, new_key)  
                label = table_label_mapping.get(new_key, f"{cell['row']} {cell['column']}")  
                field_data = {  
                    "fieldId": mapped_key,  
                    "fieldName": mapped_key,  
                    "inputType": "text",  
                    "label": label,  
                    "score": round(cell.get("confidence", 0) * 100, 1),  
                    "value": cell.get("value", "")  
                }  
                output_data["otherMedicalCoverageInfo"].append(field_data)  
        elif field.get("section") == "Medicare - Employee Information":  
            for subfield in field.get('fields', []):  
                if subfield.get("key") in medicare_employee_key_mapping:  
                    process_field(subfield, medicare_employee_key_mapping, "medicareEmployeeInfo")  
        elif field.get("section") == "Medicare - Spouse/Dependent":  
            for subfield in field.get('fields', []):  
                if subfield.get("key") in medicare_spouse_dependent_key_mapping:  
                    process_field(subfield, medicare_spouse_dependent_key_mapping, "medicareSpouseDependentInfo")  
  
    return output_data 