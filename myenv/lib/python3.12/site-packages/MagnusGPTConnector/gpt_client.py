import requests
from MagnusGPTConnector.config import Config
import base64
from openai import AzureOpenAI
import pdfplumber
import base64
import os
from io import BytesIO
from reportlab.pdfgen import canvas 
from pypdf import PdfWriter, PdfReader

def _has_form_fields(pdf_bytes):  
    pdf = PdfReader(BytesIO(pdf_bytes))  
    for page in pdf.pages:  
        if '/Annots' in page:  
            for annotation in page['/Annots']:  
                annot_object = pdf.get_object(annotation)  
                if annot_object.get('/Subtype') == '/Widget':  
                    return True  
    return False 

def _flatten_pdf_forms(pdf_file_like):  
    input_pdf = PdfReader(pdf_file_like)  
    output_pdf = PdfWriter()  
  
    for page_number, page in enumerate(input_pdf.pages):  
        page_media_box = page.mediabox  
        packet = BytesIO()  
        c = canvas.Canvas(packet, pagesize=(page_media_box.upper_right[0], page_media_box.upper_right[1]))  
        if '/Annots' in page:  
            for annotation in page['/Annots']:  
                annot_object = input_pdf.get_object(annotation) 
                if '/T' in annot_object and '/V' in annot_object:  
                    field_name = annot_object['/T']  
                    field_value = annot_object['/V']  
                    if field_value == "/Off":  
                        continue  
                    if field_value == "/Yes":  
                        field_value = "x"    
                    rect = annot_object.get('/Rect', None)  
                    if rect and field_value:  
                        x, y = float(rect[0]), float(rect[1])  
                        y += (float(rect[3]) - float(rect[1])) / 2  
                        c.setFont("Helvetica", 12)  
                        c.drawString(x, y, str(field_value))  
                        # print(f"Drawing string: {field_value} at ({x}, {y})")  
        c.save()  
        packet.seek(0)  
  
        overlay_pdf = PdfReader(packet)  
        if len(overlay_pdf.pages) > 0:  
            page.merge_page(overlay_pdf.pages[0])  
        else:  
            print(f"Warning: No pages found in the overlay PDF for page {page_number}")    

        output_pdf.add_page(page)  

    output_stream = BytesIO()  
    output_pdf.write(output_stream)  
    output_stream.seek(0)  
    return output_stream.getvalue()

def _convert_pdf_to_base64_images(pdf_bytes):  
    base64_images = []   
    
    flattened_pdf = _flatten_pdf_forms((BytesIO(pdf_bytes))) if _has_form_fields(pdf_bytes) else pdf_bytes

    # Extract and print text from the flattened PDF  
    with pdfplumber.open(BytesIO(flattened_pdf)) as pdf:  
        for page in pdf.pages:  
            text = page.extract_text()  
            # print("Extracted Text from Page:", text)  # Printing the extracted text 

    with pdfplumber.open(BytesIO(flattened_pdf)) as pdf:  
        for page in pdf.pages:  
            page_image = page.to_image(resolution=200)  
            pil_image = page_image.original 
            buffered = BytesIO()  
            pil_image.save(buffered, format="PNG")  
            img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')  
            base64_images.append(img_base64) 

            # Save the Image to check the quality - uncomment these two lines to do so
            # image_path = f"page_{page}.png"
            # pil_image.save(image_path)

    return base64_images 

class GPTClient:
    def __init__(self, config: Config):
        self.config = config

    def invoke_whisper(self, audio_file = None, audio_path = None):
        """Call Whisper model for audio-based input/output
        Args:
            audio_file : file object
                file object that needs to be processed
            audio_path : str
                file path that needs to be processed
        """
        try:
            config = self.config.get_whisper_config()

            client = AzureOpenAI(
                    azure_endpoint = f"{config['api_url']}",
                    api_key = f"{config['api_key']}",
                    api_version = f"{config['api_version']}",
                )
            deployment_id = f"{config['deployment']}"
            if audio_path and audio_file:
                raise ValueError("Please pass either path to the file or file object. Both are not supported as per current release")
            elif audio_file:
                result = client.audio.transcriptions.create(
                            file = audio_file,            
                            model=deployment_id
                            )
            elif audio_path:
                _, file_extension = os.path.splitext(audio_path)
                file_extension = file_extension.lower()
                result = client.audio.transcriptions.create(
                            file = open(audio_path, "rb"),            
                            model=deployment_id
                            )
            else:
                raise ValueError("Invalid input type. We currently take input a file object or path to the file.")
            return result
        except ValueError as ve:
            print(f"Configuration error: {ve}")
            raise ValueError(f"Configuration error: {ve}")
        except requests.RequestException as re:
            print(f"Configuration error: {re}")
            raise ValueError(f"Configuration error: {re}")
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            raise ValueError(f"An unexpected error occurred: {e}")

    def invoke_embedding(self, data):
        """ Create embeddings from documents or query 

        Args:
            data : 'List of strings' or 'string'
                data that will be converted into embeddings
        """
        try:
            config = self.config.get_embedding_config()
            
            embedding_client = AzureOpenAI(
                azure_endpoint = f"{config['api_url']}",
                api_key = f"{config['api_key']}",
                api_version = f"{config['api_version']}"
            )
            deployment_id = f"{config['deployment']}"
            
            if (isinstance(data, str)):
                return embedding_client.embeddings.create(input=data, model=deployment_id).data[0].embedding
            elif (isinstance(data, list) and all(isinstance(item, str) for item in data)):
                embeddings = embedding_client.embeddings.create(input=data, model=deployment_id)
                return [embeddings.data[i].embedding for i in range(len(embeddings.data))]
            else:
                raise ValueError("Invalid input type. Please provide either 'list of strings' or a 'string'.")

        except ValueError as ve:
            print(f"Configuration error: {ve}")
            raise ValueError(f"Configuration error: {ve}")
        except requests.RequestException as re:
            print(f"Configuration error: {re}")
            raise ValueError(f"Configuration error: {re}")
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            raise ValueError(f"An unexpected error occurred: {e}")

    def invoke_gpt_35(self, input_text, temperature = 0, top_p = 0, prompt = "No External Instructions", response_type = None):
        """ Call GPT-3.5 model for text-based input/output 
        Args:
            input_text : str
                Input query for the model.
            temperature : float
                A value between 0 and 1. Higher values increase the likelihood that the model will use more unexpected words in its response,
                simultaneously increasing creativity and randomness. By default, it is set to 0.
            top_p : float
                A value n between 0 and 1, where the model calculates probability values for each possible next token and considers the 
                possibilities with n collective probability = n. Higher values increase the number of potential tokens. By default, it is set to 0.
            prompt : str
                Prompt to be given by user to give specific instructions with respect to output. By default, it is set to "No External Instructions".
            response_type : str
                Indicates the type of response that the model will return (either plain text or JSON format). By default, it is set to None, indicating 
                plain text.
        """
        try:
            config = self.config.get_gpt_35_config()
            
            if not config['deployment']:
                raise ValueError("GPT-3.5 deployment ID is missing. Please provide it.")
            
            if not config['api_key'] or not config['api_url'] or not config['api_version']:
                raise ValueError("API key or API URL is missing. Please provide it.")
            
            if response_type == None:
                response_format = None
            elif response_type.lower() == 'json':
                input_text += " Format the response using JSON." # OpenAI's implementation requires mentioning JSON in messages when invoking the model
                response_format = {"type": "json_object"}
            else:
                raise ValueError("Invalid response type. If a non-text response type is required, please select one among the available options: 'JSON'")

            client = AzureOpenAI(
                azure_endpoint = f"{config['api_url']}",
                api_key = f"{config['api_key']}",
                api_version = f"{config['api_version']}",
            )
            # print(config)
            completion = client.chat.completions.create(
                model=config['deployment'],
                temperature = temperature,
                top_p = top_p,
                messages= [
                    {
                        "role": "system",
                        "content": f"You are an AI assistant that helps people find information. Follow the instructions if provided : {prompt}"
                    },
                    {
                        "role": "user",
                        "content": input_text
                    }
                ],
                response_format=response_format
            )
            return completion.model_dump()

        except ValueError as ve:
            print(f"Configuration error: {ve}")
            raise ValueError(f"Configuration error: {ve}")
        except requests.RequestException as re:
            print(f"Configuration error: {re}")
            raise ValueError(f"Configuration error: {re}")
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            raise ValueError(f"An unexpected error occurred: {e}")

    def invoke_gpt_4o(self, input_data = None, path_to_file = None, temperature = 0, top_p = 0, prompt = "No External Instructions", response_type = None, schema = None):
        """ Call GPT-4o model that can handle text, image, and PDF input 
            Args :
                input_data : str
                    Input data for the model. It can be text, buffer of an image, or buffer of a PDF. By default, it is deemed to be text.
                path_to_file : str
                    Path to the file (image or PDF) uploaded by user.
                temperature : float
                    A value between 0 and 1. Higher values increase the likelihood that the model will use more unexpected words in its response,
                    simultaneously increasing creativity and randomness. By default, it is set to 0.
                top_p : float
                    A value n between 0 and 1, where the model calculates probability values for each possible next token and considers the 
                    possibilities with n collective probability = n. Higher values increase the number of potential tokens. By default, it is set to 0.
                prompt : str
                    Prompt to be given by user to give specific instructions with respect to output. By default, it is set to "No External Instructions". 
                response_type : str
                    Indicates the type of response that the model will return (either plain text, JSON format, or structured output). By default, it is set 
                    to None, indicating plain text.
                schema : dict
                    Schema to be used for the response if the response_type is structured. By default, it is set to None.
        """
        try:

            config = self.config.get_gpt_4o_config()

            if not config['deployment']:
                raise ValueError("GPT-4o deployment ID is missing. Please provide it.")
                
            if not config['api_key'] or not config['api_url'] or not config['api_version']:
                raise ValueError("API key or API URL or API version is missing. Please provide it.")

            client = AzureOpenAI(
                azure_endpoint = f"{config['api_url']}",
                api_key = f"{config['api_key']}",
                api_version = f"{config['api_version']}",
            )
            system_content = "You are an AI assistant that helps people find information. Follow the instructions if provided."
            messages = [
                    {
                        "role": "system",
                        "content": system_content
                    }
                    ]
            if response_type is None:
                response_format = {"type": "text"}
            else:
                if response_type.lower() == 'json':
                    system_content += " When needed, respond in JSON format."
                    response_format = {"type": "json_object"}
                    system_content += f" {prompt}"
                    messages[0]["content"] = system_content
                elif response_type.lower() == 'structured':
                    if not schema:
                        raise ValueError("Schema is required for structured response type.")
                    response_format = schema
                else:
                    raise ValueError("Invalid response type. If a non-text response type is required, please select one among the available options: 'JSON', 'Structured'")
 
            # Route to take if the input data is text
            if isinstance(input_data, str):

                messages.append(
                    {
                        "role": "user",
                        "content": input_data
                    }     
                )
            
            # Route to take if the input data is an image or PDF
            else:
                if input_data and path_to_file:
                    raise ValueError("Please pass either path to the file or file buffer. Both are not supported as per current release")
                
                base64_images = []
                
                if path_to_file:
                    _, file_extension = os.path.splitext(path_to_file)
                    file_extension = file_extension.lower()
                    if file_extension not in ['.png', '.jpg', '.jpeg', '.pdf']:
                        raise ValueError("Invalid input type. Supported types are '.png', '.jpg', '.jpeg', and '.pdf'.")
                    
                    if file_extension == '.pdf':
                        with open(path_to_file, "rb") as pdf_file:
                            pdf_data = pdf_file.read()
                        base64_images +=_convert_pdf_to_base64_images(pdf_data)
                    else:
                        with open(path_to_file, "rb") as img_file:
                            base64_images.append(base64.b64encode(img_file.read()).decode('utf-8'))
                        
                elif input_data:
                    if not input_data.filename.endswith(('.png', '.jpg', '.jpeg', '.pdf')):
                        raise ValueError("Invalid input type. Supported types are '.png', '.jpg', '.jpeg', and '.pdf'.")
                    
                    if input_data.filename.endswith(('.pdf')):
                        input_data.seek(0)
                        pdf_data = input_data.read() 
                        base64_images +=_convert_pdf_to_base64_images(pdf_data)
                    else:
                        base64_images.append(base64.b64encode(input_data.read()).decode('utf-8'))

                messages.append(
                    {   
                        "name": "images",
                        "role": "user",  
                        "content": [  
                            {  
                                "type": "text",
                                "text": "This is an image given by the user."
                                        "Use this file to obtain available info. Extract the data in a very structured format." 
                                        f"Follow the instructions if provided : {prompt}"

                            }
                        ]  
                    }  
                ) 
                for img in base64_images:
                    messages[1]["content"].append({"type": "image_url", "image_url": {"url": f"data:image/png;base64,{img}"}})

            completion = client.beta.chat.completions.parse(
                model=config['deployment'],
                temperature=temperature,
                top_p=top_p,
                messages=messages,
                response_format=response_format
            )
            return completion.model_dump()

        except ValueError as ve:
            print(f"Configuration error: {ve}")
            raise ValueError(f"Configuration error: {ve}")
        except requests.RequestException as re:
            print(f"Configuration error: {re}")
            raise ValueError(f"Configuration error: {re}")
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            raise ValueError(f"An unexpected error occurred: {e}")
