import os
from dotenv import load_dotenv
load_dotenv()

class Config:
    def __init__(self, auth_type = None):
        if auth_type == 'CLIENT-BASED':
            self.openai_api_key = os.getenv('MAGNUS_OPENAI_API_KEY', None)
            self.openai_api_url = os.getenv('MAGNUS_OPENAI_API_URL', None)
            self.openai_api_35_version = os.getenv('MAGNUS_API_35_VERSION', None)
            self.openai_api_4o_version = os.getenv('MAGNUS_API_4o_VERSION', None)
            self.openai_embedding_version = os.getenv('MAGNUS_EMBEDDING_VERSION', None)
            self.openai_whisper_version = os.getenv('MAGNUS_WHISPER_VERSION', None)
            self.openai_gpt_35_deployment = os.getenv('GPT_35_DEPLOYMENT', None)
            self.openai_gpt_4o_deployment = os.getenv('GPT_4o_DEPLOYMENT', None)
            self.openai_embedding_name = os.getenv('MAGNUS_EMBEDDING_DEPLOYMENT', None)
            self.openai_whisper_name = os.getenv('MAGNUS_WHISPER_DEPLOYMENT', None)
        elif auth_type is None:
            self.openai_api_key = os.getenv('MAGNUS_OPENAI_API_KEY', None)
            self.openai_api_url = os.getenv('MAGNUS_OPENAI_API_URL', None)
            self.openai_api_35_version = os.getenv('MAGNUS_API_35_VERSION', None)
            self.openai_api_4o_version = os.getenv('MAGNUS_API_4o_VERSION', None)
            self.openai_embedding_version = os.getenv('MAGNUS_EMBEDDING_VERSION', None)
            self.openai_whisper_version = os.getenv('MAGNUS_WHISPER_VERSION', None)
            self.openai_gpt_35_deployment = os.getenv('GPT_35_DEPLOYMENT', None)
            self.openai_gpt_4o_deployment = os.getenv('GPT_4o_DEPLOYMENT', None)
            self.openai_embedding_name = os.getenv('MAGNUS_EMBEDDING_DEPLOYMENT', None)
            self.openai_whisper_name = os.getenv('MAGNUS_WHISPER_DEPLOYMENT', None)
        else :
            raise ValueError("Invalid auth type. Please provide auth type as 'CLIENT-BASED' or keep it untouched.")

    def load_manually(self, api_key, api_url, api_35_version, api_4o_version, api_embedding_version, api_whisper_version, gpt_35_deployment, gpt_4o_deployment, embedding_deployment, whisper_deployment):
        self.openai_api_key = api_key
        self.openai_api_url = api_url
        self.openai_api_35_version = api_35_version
        self.openai_api_4o_version = api_4o_version
        self.openai_embedding_version = api_embedding_version
        self.openai_whisper_version = api_whisper_version
        self.openai_gpt_35_deployment = gpt_35_deployment
        self.openai_gpt_4o_deployment = gpt_4o_deployment
        self.openai_embedding_name = embedding_deployment
        self.openai_whisper_name = whisper_deployment

    def get_gpt_35_config(self):
        return {
            'api_key': self.openai_api_key,
            'api_url': self.openai_api_url,
            'api_version': self.openai_api_35_version,
            'deployment': self.openai_gpt_35_deployment
        }

    def get_gpt_4o_config(self):
        return {
            'api_key': self.openai_api_key,
            'api_url': self.openai_api_url,
            'api_version': self.openai_api_4o_version,
            'deployment': self.openai_gpt_4o_deployment
        }
    
    def get_embedding_config(self):
        return {
            'api_key': self.openai_api_key,
            'api_url': self.openai_api_url,
            'api_version': self.openai_embedding_version,
            'deployment': self.openai_embedding_name
        }
    
    def get_whisper_config(self):
        return{
            'api_key': self.openai_api_key,
            'api_url': self.openai_api_url,
            'api_version': self.openai_whisper_version,
            'deployment': self.openai_whisper_name
        }
