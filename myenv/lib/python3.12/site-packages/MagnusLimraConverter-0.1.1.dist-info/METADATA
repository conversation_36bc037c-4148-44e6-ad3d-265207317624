Metadata-Version: 2.1
Name: MagnusLimraConverter
Version: 0.1.1
Summary: MagnusDocToLimraConverter is a Python package for converting Docsight payload to Limra format.
Author: <PERSON><PERSON>cal
Author-email: <EMAIL>
License: MIT
Classifier: Programming Language :: Python :: 3
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Requires-Python: >=3.7
Description-Content-Type: text/markdown
License-File: LICENSE.txt

Overview
# Magnus DocToLimraConverter

The primary goal of this package is to take a Docsight Payload  and transform it into the LIMRA format. This is achieved through a modular architecture where each section of the input is processed independently and mapped to the output format.

## Project Structure

```
MagnusLimraConverter/
├── __init__.py  
├── convert_docsight_to_limra.py      # Main script for converting payloads  
├── map_to_coverage.py                # Maps product selection/coverage data  
├── map_to_dependent.py               # Maps family/dependent information  
├── map_to_employee.py                # Maps employee information  
├── map_to_employer.py                # Maps employer information  
├── map_to_other_medical_coverage.py  # Maps other medical coverage information  
├── map_to_prior_medical_insurance_info.py  # Maps prior medical insurance data  
└── utils/  
    ├── constants.py                  # Contains reusable constants  
    ├── utils.py                      # Contains helper functions for formatting and processing  
```

## Usage 


#### Import necessary modules  
```python
  
from MagnusLimraConverter.convert_docsight_to_limra import convert_docsight_to_limra  
```
  

### Convert the Docsight payload to LIMRA format  
```python
limra_output = convert_docsight_to_limra(<INSERT DOCSIGHT PAYLOAD>)
```
  
### Print the converted LIMRA payload  
```python
import json
print(json.dumps(limra_output, indent=4))  
```



## Key Functional Components

The project processes the Docsight Payload by breaking it into sections and mapping them to corresponding LIMRA fields. The mapping logic is implemented in separate files for better modularity.

### convert_docsight_to_limra.py
- Entry point of the program.
- Iterates through the Docsight Payload and processes each section using specialized mapping functions.

### Mapping Functions
- **map_to_employer.py**: Maps `To Be Completed By Employer` section of Docsight payload.
- **map_to_employee.py**: Maps `Employee Information` section.
- **map_to_dependent.py**: Maps `Family Information` section.
- **map_to_prior_medical_insurance_info.py**: Maps `Prior Medical Insurance Information` section.
- **map_to_other_medical_coverage.py**: Maps `Other Medical Coverage Information` section.
- **map_to_coverage.py**: Maps `Product Selection` section.

### Utilities
- **utils/constants.py**: Stores reusable constants like state lists.
- **utils/utils.py**: Provides helper functions for formatting dates, phone numbers, SSNs, etc.
